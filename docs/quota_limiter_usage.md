# 配额检查和请求限速模块使用指南

## 概述

新的配额检查和请求限速模块提供了完整的用户配额管理和请求限速功能，基于数据库表设计实现，支持用户级别和组级别的配额控制。

## 核心组件

### 1. QuotaService (配额服务)

- 管理用户和组的配额分配、使用和检查
- 基于模型的 token 消耗计算配额成本
- 支持自动创建默认配额配置

### 2. RateLimiterService (限速服务)

- 基于令牌桶算法实现请求限速
- 支持用户级别和组级别的限速控制
- 支持单次查询限制和总体速率限制

### 3. QuotaLimiterManager (统一管理器)

- 整合配额检查和限速功能
- 提供统一的检查和消费接口
- 支持中间件集成

### 4. QuotaMiddleware (Gin 中间件)

- 提供 HTTP 请求级别的配额和限速检查
- 支持预检查和后消费模式
- 提供配额状态查询接口

## 数据库表说明

### 配额相关表

#### GWGroupQuotaConfig (组配额配置)

```sql
CREATE TABLE gw_group_quota_configs (
    group VARCHAR(64) PRIMARY KEY,
    quota DOUBLE NOT NULL,      -- 配额总量
    used DOUBLE NOT NULL,       -- 已使用配额
    total_used DOUBLE NOT NULL, -- 历史总使用量
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP
);
```

#### GWUserQuotaConfig (用户配额配置)

```sql
CREATE TABLE gw_user_quota_configs (
    group VARCHAR(64),
    user_id VARCHAR(64),
    quota DOUBLE NOT NULL,
    used DOUBLE NOT NULL,
    total_used DOUBLE NOT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP,
    PRIMARY KEY (group, user_id)
);
```

### 限速相关表

#### GWGroupLimitConfig (组限速配置)

```sql
CREATE TABLE gw_group_limit_configs (
    group VARCHAR(64) PRIMARY KEY,
    total_limit DOUBLE NOT NULL, -- 总体速率限制（配额/秒）
    query_limit DOUBLE NOT NULL, -- 单次查询限制（配额）
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP
);
```

#### GWUserLimitConfig (用户限速配置)

```sql
CREATE TABLE gw_user_limit_configs (
    group VARCHAR(64),
    user_id VARCHAR(64),
    total_limit DOUBLE NOT NULL,
    query_limit DOUBLE NOT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    deleted_at TIMESTAMP,
    PRIMARY KEY (group, user_id)
);
```

## 使用示例

### 1. 基本集成

```go
package main

import (
    "github.com/gin-gonic/gin"
    "goalfy_aigateway/internal/middleware"
    "goalfy_aigateway/internal/storage"
    "goalfy_aigateway/internal/quota"
)

func main() {
    // 初始化数据库
    db, err := storage.Open()
    if err != nil {
        panic(err)
    }

    // 创建配额限速管理器
    quotaManager := quota.NewQuotaLimiterManager(db)

    // 启动清理例程
    quotaManager.StartCleanupRoutine()

    // 创建中间件
    quotaMiddleware := middleware.NewQuotaMiddleware(quotaManager)

    // 创建 Gin 路由
    r := gin.Default()

    // 应用中间件
    r.Use(quotaMiddleware.QuotaCheckMiddleware())

    // API 路由组
    api := r.Group("/v1")
    {
        // 聊天完成接口
        api.POST("/chat/completions",
            quotaMiddleware.PreQuotaCheckMiddleware(),
            chatCompletionHandler,
            quotaMiddleware.PostQuotaConsumeMiddleware(),
        )

        // 配额状态查询
        api.GET("/quota/status", quotaMiddleware.QuotaStatusHandler())

        // 管理员接口
        admin := api.Group("/admin")
        {
            admin.POST("/quota/reset/:group", quotaMiddleware.AdminResetQuotaHandler())
        }
    }

    r.Run(":8080")
}
```

### 2. 聊天完成处理器示例

```go
func chatCompletionHandler(c *gin.Context) {
    // 解析请求
    var req ChatCompletionRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(400, gin.H{"error": err.Error()})
        return
    }

    // 设置模型信息到上下文（用于配额检查）
    c.Set("provider", "openai")
    c.Set("model", req.Model)
    c.Set("input_tokens", estimateInputTokens(req.Messages))
    c.Set("output_tokens", req.MaxTokens) // 预估输出token

    // 调用模型服务
    response, err := callModelService(req)
    if err != nil {
        c.JSON(500, gin.H{"error": err.Error()})
        return
    }

    // 设置实际使用的token数（用于配额消费）
    c.Set("actual_input_tokens", response.Usage.PromptTokens)
    c.Set("actual_output_tokens", response.Usage.CompletionTokens)

    c.JSON(200, response)
}
```

### 3. 直接使用服务

```go
// 创建管理器
manager := quota.NewQuotaLimiterManager(db)

// 检查配额和限速
req := &quota.CheckRequest{
    UserID:       "user123",
    Group:        "premium",
    Provider:     "openai",
    Model:        "gpt-4",
    InputTokens:  100,
    OutputTokens: 200,
}

result, err := manager.CheckAndReserve(req)
if err != nil {
    // 处理错误
    return
}

if !result.Allowed {
    // 请求被拒绝
    fmt.Printf("Request denied: %s\n", result.Message)
    return
}

// 请求通过，执行业务逻辑
// ...

// 消费配额
err = manager.ConsumeQuota(req)
if err != nil {
    // 处理错误
}
```

## 配置说明

### 默认配置值

- **用户默认配额**: 100,000 配额单位
- **组默认配额**: 500,000 配额单位
- **用户默认限速**: 1,000 配额/秒，单次查询 100 配额
- **组默认限速**: 5,000 配额/秒，单次查询 500 配额

### 配额计算公式

```
配额消耗 = 输入token数 / 模型输入配额比率 + 输出token数 / 模型输出配额比率
```

### 限速说明

- **TotalLimit**: 每秒允许的总配额消耗量
- **QueryLimit**: 单次查询允许的最大配额消耗
- **值为 -1**: 表示不限制

## API 接口

### 1. 获取配额状态

```http
GET /v1/quota/status
Headers:
  X-User-ID: user123
  X-Group: premium

Response:
{
  "user_id": "user123",
  "group": "premium",
  "status": {
    "user": {
      "quota": 100000,
      "used": 1500,
      "remaining": 98500,
      "total_used": 15000
    },
    "group": {
      "quota": 500000,
      "used": 50000,
      "remaining": 450000,
      "total_used": 200000
    }
  },
  "timestamp": 1640995200
}
```

### 2. 重置组配额（管理员）

```http
POST /v1/admin/quota/reset/premium

Response:
{
  "message": "Quota reset successfully",
  "group": "premium",
  "timestamp": 1640995200
}
```

### 3. 配额不足响应

```http
HTTP/1.1 402 Payment Required
{
  "error": "配额不足: 用户配额不足",
  "code": "QUOTA_EXCEEDED",
  "quota_cost": 15.5,
  "retry_after": 3600,
  "details": {
    "quota_allowed": false,
    "limiter_allowed": true
  }
}
```

### 4. 限速超限响应

```http
HTTP/1.1 429 Too Many Requests
Retry-After: 60
{
  "error": "限速超限: 用户限速超限",
  "code": "RATE_LIMIT_EXCEEDED",
  "quota_cost": 10.0,
  "retry_after": 60,
  "details": {
    "quota_allowed": true,
    "limiter_allowed": false
  }
}
```

## 运维管理

### 1. 定期重置配额

```go
// 每天重置所有组的配额
func resetDailyQuota() {
    groups := []string{"default", "premium", "enterprise"}
    for _, group := range groups {
        err := manager.ResetQuota(group)
        if err != nil {
            log.Printf("Failed to reset quota for group %s: %v", group, err)
        }
    }
}
```

### 2. 监控配额使用

```go
// 获取配额使用情况
status, err := manager.GetStatus("user123", "premium")
if err != nil {
    log.Printf("Failed to get quota status: %v", err)
    return
}

// 检查配额使用率
userUsage := status["user"].(map[string]interface{})
usageRate := userUsage["used"].(float64) / userUsage["quota"].(float64)
if usageRate > 0.8 {
    // 发送告警
    sendQuotaAlert("user123", usageRate)
}
```

### 3. 动态调整限速

```go
// 为特定用户调整限速配置
err := manager.UpdateLimitConfig("user123", "premium", 2000.0, 200.0)
if err != nil {
    log.Printf("Failed to update limit config: %v", err)
}
```

## 注意事项

1. **性能考虑**: 限速器使用内存存储，定期清理避免内存泄漏
2. **数据一致性**: 配额更新使用数据库事务确保一致性
3. **错误处理**: 配额消费失败不影响响应，采用异步处理
4. **监控告警**: 建议监控配额使用率和限速触发频率
5. **扩展性**: 支持多实例部署，但限速器状态不共享
