# AI Gateway Admin UI 配置完成

## 📋 配置总结

已成功调整前后端实现，使UI使用admin前缀，静态文件路径为 `ui/dist`。

## 🔧 主要变更

### 后端变更 (internal/api/server.go)

1. **API路由前缀调整**：
   - 从 `/admin` 改为 `/admin/api`
   - 避免与前端路由冲突

2. **静态文件服务**：
   - 静态资源路径：`/admin/assets/*` → `./ui/dist/assets`
   - 主页面文件：`./ui/dist/index.html`

3. **SPA路由支持**：
   - `/admin` 重定向到 `/admin/`
   - `/admin/*` (非API路径) 返回 `index.html`
   - 支持前端路由

### 前端变更

1. **Vite配置 (ui/vite.config.js)**：
   - 添加 `base: '/admin/'`
   - 代理路径：`/admin/api`

2. **路由配置 (ui/src/router/index.js)**：
   - 设置 `history: createWebHistory('/admin/')`

3. **API配置 (ui/src/utils/api.js)**：
   - 基础URL：`/admin/api`

## 🌐 访问路径

### 用户访问
- **Admin UI**: http://localhost:8080/admin/
- **登录密钥**: 123456 (配置文件中设置)

### API接口
- **基础路径**: `/admin/api`
- **登录接口**: `POST /admin/api/login`
- **供应商管理**: `/admin/api/providers`
- **模型组管理**: `/admin/api/model-groups`

### 静态资源
- **CSS/JS文件**: `/admin/assets/*`

## 🧪 测试结果

所有功能测试通过：
- ✅ `/admin` 重定向到 `/admin/`
- ✅ `/admin/` 返回 index.html
- ✅ 静态资源访问正常
- ✅ API接口正常工作
- ✅ SPA路由支持正常
- ✅ 健康检查接口正常

## 🚀 部署说明

### 开发环境
```bash
# 启动后端服务器
go run cmd/gateway/main.go

# 启动前端开发服务器
cd ui && npm run dev
```

### 生产环境
```bash
# 构建前端
cd ui && npm run build

# 启动后端服务器（会自动服务静态文件）
go run cmd/gateway/main.go
```

### Nginx配置示例
```nginx
server {
    listen 80;
    server_name admin.your-domain.com;
    root /path/to/ui/dist;
    index index.html;

    # Admin UI 路由支持
    location /admin/ {
        try_files $uri $uri/ /index.html;
    }

    # 重定向根路径到admin
    location = / {
        return 301 /admin/;
    }

    # API 代理到后端
    location /admin/api/ {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 📁 文件结构

```
goalfy-ai-gateway/
├── ui/
│   ├── dist/                    # 构建输出目录
│   │   ├── assets/             # 静态资源
│   │   └── index.html          # 主页面
│   ├── src/
│   │   ├── router/index.js     # 路由配置 (base: '/admin/')
│   │   └── utils/api.js        # API配置 (baseURL: '/admin/api')
│   └── vite.config.js          # Vite配置 (base: '/admin/')
├── internal/api/server.go      # 服务器配置
└── test_admin_ui.sh           # 测试脚本
```

## 🔍 故障排除

### 常见问题

1. **静态资源404**：
   - 确保 `ui/dist` 目录存在且包含构建文件
   - 检查文件权限

2. **API调用失败**：
   - 确认API路径为 `/admin/api/*`
   - 检查后端服务器是否运行

3. **前端路由不工作**：
   - 确认SPA路由配置正确
   - 检查NoRoute处理逻辑

### 测试命令
```bash
# 运行完整测试
./test_admin_ui.sh

# 手动测试
curl http://localhost:8080/admin/
curl -X POST http://localhost:8080/admin/api/login -H "Content-Type: application/json" -d '{"key": "123456"}'
```

## 🐛 问题修复

### API路径重复问题
**问题**: 前端代码中API调用路径包含重复的 `/admin/` 前缀，导致实际请求URL变成 `/admin/api/admin/providers`

**原因**:
- `api.js` 中 `baseURL` 设置为 `/admin/api`
- 前端代码中API调用仍使用 `/admin/providers` 等路径
- 导致最终URL为 `/admin/api` + `/admin/providers` = `/admin/api/admin/providers`

**解决方案**:
修复所有前端文件中的API调用路径，移除多余的 `/admin/` 前缀：
- `ui/src/views/Providers.vue` - 9处修复
- `ui/src/views/ModelGroups.vue` - 7处修复
- `ui/src/views/ProviderModels.vue` - 5处修复
- `ui/src/stores/auth.js` - 1处修复

**修复示例**:
```javascript
// 修复前
await api.get('/admin/providers')  // 实际请求: /admin/api/admin/providers

// 修复后
await api.get('/providers')       // 实际请求: /admin/api/providers
```

## ✅ 配置完成

Admin UI现在已正确配置为使用 `/admin/` 前缀，所有功能正常工作！API路径重复问题已完全修复。
