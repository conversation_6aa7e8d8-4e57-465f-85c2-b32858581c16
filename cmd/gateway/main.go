package main

import (
	"context"
	"github.com/spf13/pflag"
	"goalfy_aigateway/internal/api"
	"goalfy_aigateway/internal/config"
	"goalfy_aigateway/pkg/otel_init"
	"log"
)

var ctx context.Context = context.Background()

func main() {

	var addr, configPath string
	pflag.StringVar(&addr, "addr", ":8080", "http listen address")
	pflag.StringVar(&configPath, "config", "etc/config.yaml", "config file path")
	pflag.Parse()
	err := config.LoadConfig(configPath)
	if err != nil {
		log.Fatal(err)
	}
	if err := otel_init.Init(ctx, config.GetConfig().Otel); err != nil {
		log.Fatal("init otel:", err)
	}
	if err := api.Run(addr); err != nil {
		log.Fatalf("服务启动失败: %v", err)
	}
	otel_init.Shutdown()
}
