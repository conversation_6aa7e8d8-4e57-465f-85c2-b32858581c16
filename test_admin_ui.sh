#!/bin/bash

# AI Gateway Admin UI 测试脚本

echo "🧪 测试 AI Gateway Admin UI 配置"
echo "================================"

BASE_URL="http://localhost:8080"

# 测试1: 检查 /admin 重定向到 /admin/
echo "📍 测试1: 检查 /admin 重定向..."
REDIRECT_RESPONSE=$(curl -s -I "$BASE_URL/admin" | grep "Location:")
if [[ $REDIRECT_RESPONSE == *"/admin/"* ]]; then
    echo "✅ /admin 正确重定向到 /admin/"
else
    echo "❌ /admin 重定向失败"
fi

# 测试2: 检查 /admin/ 返回 index.html
echo "📍 测试2: 检查 /admin/ 返回 index.html..."
ADMIN_RESPONSE=$(curl -s "$BASE_URL/admin/")
if [[ $ADMIN_RESPONSE == *"AI Gateway Admin"* ]]; then
    echo "✅ /admin/ 正确返回 index.html"
else
    echo "❌ /admin/ 未返回正确的 index.html"
fi

# 测试3: 检查静态资源访问
echo "📍 测试3: 检查静态资源访问..."
CSS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/admin/assets/css/index-dbabe959.css")
JS_STATUS=$(curl -s -o /dev/null -w "%{http_code}" "$BASE_URL/admin/assets/js/index-07abc85e.js")

if [[ $CSS_STATUS == "200" ]]; then
    echo "✅ CSS 文件访问正常"
else
    echo "❌ CSS 文件访问失败 (状态码: $CSS_STATUS)"
fi

if [[ $JS_STATUS == "200" ]]; then
    echo "✅ JS 文件访问正常"
else
    echo "❌ JS 文件访问失败 (状态码: $JS_STATUS)"
fi

# 测试4: 检查 API 接口
echo "📍 测试4: 检查 API 接口..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/admin/api/login" \
    -H "Content-Type: application/json" \
    -d '{"key": "123456"}')

if [[ $LOGIN_RESPONSE == *"success\":true"* ]]; then
    echo "✅ 登录 API 正常工作"
else
    echo "❌ 登录 API 失败"
fi

# 测试5: 检查 SPA 路由支持
echo "📍 测试5: 检查 SPA 路由支持..."
SPA_RESPONSE=$(curl -s "$BASE_URL/admin/providers")
if [[ $SPA_RESPONSE == *"AI Gateway Admin"* ]]; then
    echo "✅ SPA 路由支持正常"
else
    echo "❌ SPA 路由支持失败"
fi

# 测试6: 检查健康检查接口
echo "📍 测试6: 检查健康检查接口..."
HEALTH_RESPONSE=$(curl -s "$BASE_URL/health")
if [[ $HEALTH_RESPONSE == *"ok"* ]]; then
    echo "✅ 健康检查接口正常"
else
    echo "❌ 健康检查接口失败"
fi

echo ""
echo "🎉 测试完成！"
echo ""
echo "📝 访问说明："
echo "   - Admin UI: http://localhost:8080/admin/"
echo "   - 登录密钥: 123456"
echo "   - API 基础路径: /admin/api"
echo ""
echo "🔧 开发环境："
echo "   - 前端开发服务器: npm run dev (端口 3000)"
echo "   - 后端服务器: go run cmd/gateway/main.go (端口 8080)"
