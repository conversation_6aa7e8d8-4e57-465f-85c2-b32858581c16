package otel_init

import (
	"context"
	"fmt"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/exporters/otlp/otlplog/otlploggrpc"
	"go.opentelemetry.io/otel/exporters/otlp/otlpmetric/otlpmetricgrpc"
	"go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc"
	"go.opentelemetry.io/otel/log"
	"go.opentelemetry.io/otel/propagation"
	sdklog "go.opentelemetry.io/otel/sdk/log"
	sdkmetric "go.opentelemetry.io/otel/sdk/metric"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.34.0"
	"goalfy_aigateway/pkg/utils"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

type OtelConfig struct {
	ServiceName    string
	GrpcAddress    string
	ServiceVersion string
	DeployEnv      string
}

var globalLoggerProvider *sdklog.LoggerProvider
var shutdownMeterProvider func(context.Context) error
var shutdownTracerProvider func(context.Context) error
var shutdownLoggerProvider func(context.Context) error

func GetLogger(instrumentationName string) log.Logger {
	if globalLoggerProvider == nil {
		panic("日志提供器未初始化，请先调用 initLoggerProvider")
	}
	return globalLoggerProvider.Logger(instrumentationName)
}

func initConn(addr string) (*grpc.ClientConn, error) {
	// It connects the OpenTelemetry Collector through local gRPC connection.
	// You may replace `localhost:4317` with your endpoint.
	conn, err := grpc.NewClient(addr,
		// Note the use of insecure transport here. TLS is recommended in production.
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create gRPC connection to collector: %w", err)
	}

	return conn, err
}

func initTracerProvider(ctx context.Context, res *resource.Resource, conn *grpc.ClientConn) (func(context.Context) error, error) {
	// Set up a trace exporter
	traceExporter, err := otlptracegrpc.New(ctx, otlptracegrpc.WithGRPCConn(conn))
	if err != nil {
		return nil, fmt.Errorf("failed to create trace exporter: %w", err)
	}

	// Register the trace exporter with a TracerProvider, using a batch
	// span processor to aggregate spans before export.
	bsp := sdktrace.NewBatchSpanProcessor(traceExporter)
	tracerProvider := sdktrace.NewTracerProvider(
		sdktrace.WithSampler(sdktrace.AlwaysSample()),
		sdktrace.WithResource(res),
		sdktrace.WithSpanProcessor(bsp),
	)
	otel.SetTracerProvider(tracerProvider)

	// Set global propagator to tracecontext (the default is no-op).
	otel.SetTextMapPropagator(propagation.TraceContext{})

	// Shutdown will flush any remaining spans and shut down the exporter.
	return tracerProvider.Shutdown, nil
}

func initMeterProvider(ctx context.Context, res *resource.Resource, conn *grpc.ClientConn) (func(context.Context) error, error) {
	metricExporter, err := otlpmetricgrpc.New(ctx, otlpmetricgrpc.WithGRPCConn(conn))
	if err != nil {
		return nil, fmt.Errorf("failed to create metrics exporter: %w", err)
	}

	meterProvider := sdkmetric.NewMeterProvider(
		sdkmetric.WithReader(sdkmetric.NewPeriodicReader(metricExporter)),
		sdkmetric.WithResource(res),
	)
	otel.SetMeterProvider(meterProvider)

	return meterProvider.Shutdown, nil
}

func initLoggerProvider(ctx context.Context, res *resource.Resource, conn *grpc.ClientConn) (func(context.Context) error, error) {
	// 创建日志导出器（使用已有的 gRPC 连接）
	logExporter, err := otlploggrpc.New(
		ctx,
		otlploggrpc.WithGRPCConn(conn), // 复用 gRPC 连接，与追踪/指标共享
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create log exporter: %w", err)
	}

	// 创建批量日志处理器（聚合日志后导出，提高性能）
	batchProcessor := sdklog.NewBatchProcessor(logExporter)

	// 初始化日志提供器
	loggerProvider := sdklog.NewLoggerProvider(
		sdklog.WithResource(res),             // 关联资源信息（如服务名）
		sdklog.WithProcessor(batchProcessor), // 绑定日志处理器
	)
	globalLoggerProvider = loggerProvider
	// 返回关闭函数：关闭日志提供器，确保缓存的日志被刷新
	return loggerProvider.Shutdown, nil
}

func Init(ctx context.Context, cfg OtelConfig) error {
	localIP := utils.GetLocalIP()
	serviceNameKey := semconv.ServiceNameKey.String(cfg.ServiceName)
	serviceVersion := semconv.ServiceVersionKey.String(cfg.ServiceVersion)
	deployEnv := semconv.DeploymentEnvironmentName(cfg.DeployEnv)
	hostIpKey := semconv.HostIPKey.String(localIP)
	res, err := resource.New(ctx,
		resource.WithAttributes(
			serviceNameKey,
			serviceVersion,
			deployEnv,
			hostIpKey,
		),
	)
	conn, err := initConn(cfg.GrpcAddress)
	if err != nil {
		return err
	}
	shutdownMeterProvider, err = initMeterProvider(ctx, res, conn)
	if err != nil {
		return err
	}
	shutdownTracerProvider, err = initTracerProvider(ctx, res, conn)
	if err != nil {
		return err
	}
	shutdownLoggerProvider, err = initLoggerProvider(ctx, res, conn)
	if err != nil {
		return err
	}
	return nil
}

func Shutdown() {
	if shutdownMeterProvider != nil {
		err := shutdownMeterProvider(context.Background())
		if err != nil {
			fmt.Println("failed to shutdown meter provider:", err.Error())
		}
	}
	if shutdownTracerProvider != nil {
		err := shutdownTracerProvider(context.Background())
		if err != nil {
			fmt.Println("failed to shutdown tracer provider:", err.Error())
		}

	}
	if shutdownLoggerProvider != nil {
		err := shutdownLoggerProvider(context.Background())
		if err != nil {
			fmt.Println("failed to shutdown logger provider:", err.Error())
		}
	}
}
