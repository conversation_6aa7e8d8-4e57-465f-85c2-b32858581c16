package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"errors"
)

var encoding = base64.NewEncoding("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz!#").WithPadding(base64.NoPadding)

func AesEncode(k string, aesKeyV1 []byte) (string, error) {
	// 创建密码块
	block, err := aes.NewCipher(aesKeyV1)
	if err != nil {
		return "", err
	}

	// 填充数据以满足块大小要求
	data := []byte(k)
	padding := aes.BlockSize - len(data)%aes.BlockSize
	padtext := make([]byte, padding)
	for i := range padtext {
		padtext[i] = byte(padding)
	}
	data = append(data, padtext...)

	// 使用CBC模式
	iv := make([]byte, aes.BlockSize) // 初始化向量
	stream := cipher.NewCBCEncrypter(block, iv)

	// 加密
	dest := make([]byte, len(data))
	stream.CryptBlocks(dest, data)

	return encoding.EncodeToString(dest), nil
}

func AesDecode(k string, aesKey []byte) (string, error) {
	// 解码base64
	data, err := encoding.DecodeString(k)
	if err != nil {
		return "", err
	}

	// 创建密码块
	block, err := aes.NewCipher([]byte(aesKey))
	if err != nil {
		return "", err
	}

	// 检查数据长度
	if len(data) < aes.BlockSize {
		return "", errors.New("数据太短")
	}

	// 使用CBC模式
	iv := make([]byte, aes.BlockSize)
	stream := cipher.NewCBCDecrypter(block, iv)

	// 解密
	dest := make([]byte, len(data))
	stream.CryptBlocks(dest, data)

	// 去除填充
	padding := int(dest[len(dest)-1])
	if padding > len(dest) {
		return "", errors.New("无效的填充")
	}
	return string(dest[:len(dest)-padding]), nil
}
