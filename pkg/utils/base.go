package utils

import (
	"net"
	"strings"
)

func IsInSlice[T comparable](item T, list []T) bool {
	for _, v := range list {
		if v == item {
			return true
		}
	}
	return false
}

func StringP(v string) *string {
	return &v
}

func GetLocalIP() string {
	con, err := net.Dial("udp", "*******:53")
	if err != nil {
		panic("failed to connect to local ip")
	}
	defer con.Close()
	addr := con.LocalAddr().String()
	return strings.Split(addr, ":")[0]
}
