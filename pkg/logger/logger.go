package logger

import (
	"go.uber.org/zap"
)

var zapLogger, _ = zap.NewDevelopment(zap.AddStacktrace(zap.DPanicLevel), zap.AddCallerSkip(1))

func Info(msg string, fields ...zap.Field) {
	zapLogger.Info(msg, fields...)
}

func Debug(msg string, fields ...zap.Field) {
	zapLogger.Debug(msg, fields...)
}

func Warn(msg string, fields ...zap.Field) {
	zapLogger.Warn(msg, fields...)
}

func Error(msg string, fields ...zap.Field) {
	zapLogger.Error(msg, fields...)
}

func Fatal(msg string, fields ...zap.Field) {
	zapLogger.Fatal(msg, fields...)
}
