package admin

import (
	"github.com/gin-gonic/gin"
	"goalfy_aigateway/internal/models"
	"goalfy_aigateway/internal/services/router"
	"goalfy_aigateway/internal/storage"
	"net/http"
)

// ModelGroupHandler 模型组处理器
type ModelGroupHandler struct {
	dao     *storage.ModelGroupStorage
	service *router.WeightedModelRouter
}

// NewModelGroupHandler 创建模型组处理器
func NewModelGroupHandler(db storage.CommonDao, modelService *router.WeightedModelRouter) *ModelGroupHandler {
	return &ModelGroupHandler{dao: storage.NewModelGroupStorage(db), service: modelService}
}

// UpdateModelGroupStatusRequest 更新模型组状态请求
type UpdateModelGroupStatusRequest struct {
	Enabled bool `json:"enabled"`
}

// GetModelGroups 获取模型组列表
func (h *ModelGroupHandler) GetModelGroups(c *gin.Context) {
	groups, err := h.dao.GetModelGroups(nil)
	if err != nil {

	}
	var lst []*models.ModelGroupConfig
	for _, v := range groups {
		lst = append(lst, v)
	}
	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"data":      lst,
		"total":     len(lst),
		"page":      1,
		"page_size": 100,
	})
}

// CreateModelGroup 创建模型组
func (h *ModelGroupHandler) CreateModelGroup(c *gin.Context) {
	var req models.ModelGroupConfig
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证策略
	if req.Strategy != "random" && req.Strategy != "weighted" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的策略类型",
		})
		return
	}
	final, err := h.service.CreateGroup(c, req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "创建失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "创建成功",
		"data":    final,
	})
}

// UpdateModelGroup 更新模型组
func (h *ModelGroupHandler) UpdateModelGroup(c *gin.Context) {
	var req models.ModelGroupConfig
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证策略
	if req.Strategy != "random" && req.Strategy != "weighted" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的策略类型",
		})
		return
	}
	final, err := h.service.UpdateGroup(c, req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "创建失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "创建成功",
		"data":    final,
	})
}

// DeleteModelGroup 删除模型组
func (h *ModelGroupHandler) DeleteModelGroup(c *gin.Context) {
	//	name := c.Param("name")
	//
	//	// 查找模型组
	//	var modelGroup models.GWModelGroup
	//	err := h.db.Where("name = ?", name).First(&modelGroup).Error
	//	if err != nil {
	//		if err == gorm.ErrRecordNotFound {
	//			c.JSON(http.StatusNotFound, gin.H{
	//				"success": false,
	//				"message": "模型组不存在",
	//			})
	//		} else {
	//			c.JSON(http.StatusInternalServerError, gin.H{
	//				"success": false,
	//				"message": "查询模型组失败",
	//				"error":   err.Error(),
	//			})
	//		}
	//		return
	//	}
	//
	//	// 开始事务
	//	tx := h.db.Begin()
	//	defer func() {
	//		if r := recover(); r != nil {
	//			tx.Rollback()
	//		}
	//	}()
	//
	//	// 删除模型组项目
	//	err = tx.Where("group_id = ?", modelGroup.ID).Delete(&models.GWModelGroupItem{}).Error
	//	if err != nil {
	//		tx.Rollback()
	//		c.JSON(http.StatusInternalServerError, gin.H{
	//			"success": false,
	//			"message": "删除模型组项目失败",
	//			"error":   err.Error(),
	//		})
	//		return
	//	}
	//
	//	// 删除模型组
	//	err = tx.Delete(&modelGroup).Error
	//	if err != nil {
	//		tx.Rollback()
	//		c.JSON(http.StatusInternalServerError, gin.H{
	//			"success": false,
	//			"message": "删除模型组失败",
	//			"error":   err.Error(),
	//		})
	//		return
	//	}
	//
	//	// 提交事务
	//	err = tx.Commit().Error
	//	if err != nil {
	//		c.JSON(http.StatusInternalServerError, gin.H{
	//			"success": false,
	//			"message": "提交事务失败",
	//			"error":   err.Error(),
	//		})
	//		return
	//	}
	//
	//	c.JSON(http.StatusOK, gin.H{
	//		"success": true,
	//		"message": "删除成功",
	//	})
}
