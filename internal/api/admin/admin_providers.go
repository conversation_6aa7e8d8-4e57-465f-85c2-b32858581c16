package admin

import (
	"goalfy_aigateway/internal/services/adapters"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"goalfy_aigateway/internal/models"
	"goalfy_aigateway/internal/storage"
	"gorm.io/gorm"
)

// ProviderHandler 供应商处理器
type ProviderHandler struct {
	db *storage.ProviderStorage
	p  *adapters.ProviderService
}

// NewProviderHandler 创建供应商处理器
func NewProviderHandler(dao storage.CommonDao, p *adapters.ProviderService) *ProviderHandler {
	return &ProviderHandler{db: storage.NewProviderStorage(dao), p: p}
}

// CreateProviderRequest 创建供应商请求
type CreateProviderRequest struct {
	Name       string `json:"name" binding:"required"`
	ApiType    string `json:"api_type" binding:"required"`
	BaseUrl    string `json:"base_url" binding:"required"`
	ApiKey     string `json:"api_key" binding:"required"`
	Priority   int    `json:"priority"`
	Timeout    int    `json:"timeout"`
	MaxRetries int    `json:"max_retries"`
	Status     string `json:"status"`
}

// UpdateProviderRequest 更新供应商请求
type UpdateProviderRequest struct {
	ApiType       string `json:"api_type"`
	BaseUrl       string `json:"base_url"`
	ApiKey        string `json:"api_key"`
	Priority      int    `json:"priority"`
	Timeout       int    `json:"timeout"`
	MaxRetries    int    `json:"max_retries"`
	Status        string `json:"status"`
	CipherVersion int    `json:"cipher_version"`
}

// UpdateStatusRequest 更新状态请求
type UpdateStatusRequest struct {
	Status string `json:"status" binding:"required"`
}

// GetProviders 获取供应商列表
func (h *ProviderHandler) GetProviders(c *gin.Context) {
	var providers []*models.GWProvider

	// 分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	offset := (page - 1) * pageSize

	// 查询条件
	query := h.db.Model(&models.GWProvider{})

	// 状态过滤
	if status := c.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	// 获取总数
	var total int64
	query.Count(&total)

	// 获取数据
	err := query.Offset(offset).Limit(pageSize).Order("priority ASC, created_at DESC").Find(&providers).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取供应商列表失败",
			"error":   err.Error(),
		})
		return
	}
	for _, provider := range providers {
		provider.ApiKey = ""
	}
	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"data":      providers,
		"total":     total,
		"page":      page,
		"page_size": pageSize,
	})
}

// CreateProvider 创建供应商
func (h *ProviderHandler) CreateProvider(c *gin.Context) {
	var req CreateProviderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 设置默认值
	if req.Priority == 0 {
		req.Priority = 100
	}
	if req.Timeout == 0 {
		req.Timeout = 30000
	}
	if req.MaxRetries == 0 {
		req.MaxRetries = 3
	}
	if req.Status == "" {
		req.Status = "active"
	}

	provider := models.GWProvider{
		Name:       strings.TrimSpace(req.Name),
		ApiType:    req.ApiType,
		BaseUrl:    strings.TrimSpace(req.BaseUrl),
		ApiKey:     strings.TrimSpace(req.ApiKey),
		Priority:   req.Priority,
		Timeout:    req.Timeout,
		MaxRetries: req.MaxRetries,
		Status:     req.Status,
	}

	err := h.p.AddProvider(c, provider)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"message": "创建供应商失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "创建成功",
		"data":    provider,
	})
}

// UpdateProvider 更新供应商
func (h *ProviderHandler) UpdateProvider(c *gin.Context) {
	name := c.Param("name")

	var req UpdateProviderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 查找供应商
	var provider models.GWProvider
	err := h.db.Where("name = ?", name).First(&provider).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "供应商不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "查询供应商失败",
				"error":   err.Error(),
			})
		}
		return
	}

	// 更新字段
	if req.ApiType != "" {
		provider.ApiType = req.ApiType
	}
	if req.BaseUrl != "" {
		provider.BaseUrl = strings.TrimSpace(req.BaseUrl)
	}
	if req.ApiKey != "" {
		provider.ApiKey = strings.TrimSpace(req.ApiKey)
	}
	if req.Priority != 0 {
		provider.Priority = req.Priority
	}
	if req.Timeout != 0 {
		provider.Timeout = req.Timeout
	}

	provider.MaxRetries = req.MaxRetries

	if req.Status != "" {
		provider.Status = req.Status
	}

	err = h.p.UpdateProvider(c, provider)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新供应商失败",
			"error":   err.Error(),
		})
		return
	}

	// 重新查询更新后的数据
	h.db.Where("name = ?", name).First(&provider)
	provider.ApiKey = ""
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新成功",
		"data":    provider,
	})
}

// DeleteProvider 删除供应商
func (h *ProviderHandler) DeleteProvider(c *gin.Context) {
	name := c.Param("name")

	// 检查是否存在关联的模型配置
	var provider models.GWProvider
	err := h.db.Where("provider = ?", name).First(&provider).Error
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "获取失败",
		})
		return
	}
	var count int64
	h.db.Select("id").Joins("").Where("provider = ?", name).Count(&count)
	if count > 0 {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "模型组使用中",
		})
		return
	}

	err = h.p.DeleteProvider(c, provider)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新状态失败",
			"error":   err.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除成功",
	})
}
