package admin

import (
	"goalfy_aigateway/internal/services/router"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"goalfy_aigateway/internal/models"
	"goalfy_aigateway/internal/storage"
	"gorm.io/gorm"
)

// CreateModelRequest 创建模型请求
type CreateModelRequest struct {
	Provider             string  `json:"provider" binding:"required"`
	ModelName            string  `json:"model_name" binding:"required"`
	TokensInputPerQuota  float64 `json:"tokens_input_per_quota" binding:"required"`
	TokensOutputPerQuota float64 `json:"tokens_output_per_quota" binding:"required"`
	MaxTokens            int     `json:"max_tokens"`
	Temperature          float64 `json:"temperature"`
	TopP                 float64 `json:"top_p"`
	Enabled              bool    `json:"enabled"`
}

// UpdateModelRequest 更新模型请求
type UpdateModelRequest struct {
	TokensInputPerQuota  *float64 `json:"tokens_input_per_quota"`
	TokensOutputPerQuota *float64 `json:"tokens_output_per_quota"`
	MaxTokens            *int     `json:"max_tokens"`
	Temperature          *float64 `json:"temperature"`
	TopP                 *float64 `json:"top_p"`
	Enabled              bool     `json:"enabled"`
}

// UpdateModelStatusRequest 更新模型状态请求
type UpdateModelStatusRequest struct {
	Enabled bool `json:"enabled"`
}

type ModelsHandler struct {
	db *storage.ProviderStorage
	p  *router.WeightedModelRouter
}

// NewModelsHandler
func NewModelsHandler(dao storage.CommonDao, p *router.WeightedModelRouter) *ModelsHandler {
	return &ModelsHandler{db: storage.NewProviderStorage(dao), p: p}
}

// GetProviderModels 获取供应商的模型列表
func (h *ModelsHandler) GetProviderModels(c *gin.Context) {
	providerName := c.Param("provider")

	var modelList []models.GWModelConfig
	err := h.db.Where("provider = ?", providerName).Find(&modelList).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取模型列表失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    modelList,
	})
}

// CreateProviderModel 创建供应商模型
func (h *ModelsHandler) CreateProviderModel(c *gin.Context) {
	providerName := c.Param("provider")

	var req CreateModelRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 确保供应商名称一致
	req.Provider = providerName

	// 设置默认值
	if req.MaxTokens == 0 {
		req.MaxTokens = 4096
	}
	if req.Temperature == 0 {
		req.Temperature = 0.7
	}
	if req.TopP == 0 {
		req.TopP = 1.0
	}

	model := models.GWModelConfig{
		Provider:             strings.TrimSpace(req.Provider),
		ModelName:            strings.TrimSpace(req.ModelName),
		TokensInputPerQuota:  req.TokensInputPerQuota,
		TokensOutputPerQuota: req.TokensOutputPerQuota,
		MaxTokens:            req.MaxTokens,
		Temperature:          req.Temperature,
		TopP:                 req.TopP,
		Enabled:              req.Enabled,
	}

	// 检查供应商是否存在
	var provider models.GWProvider
	err := h.db.Where("name = ?", providerName).First(&provider).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "供应商不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "查询供应商失败",
				"error":   err.Error(),
			})
		}
		return
	}

	err = h.p.AddModel(c, &model)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "创建模型失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "创建成功",
		"data":    model,
	})
}

// UpdateProviderModel 更新供应商模型
func (h *ModelsHandler) UpdateProviderModel(c *gin.Context) {
	providerName := c.Param("provider")
	modelName := c.Param("model")

	var req UpdateModelRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 查找模型
	var model models.GWModelConfig
	err := h.db.Where("provider = ? AND model_name = ?", providerName, modelName).First(&model).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "模型不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "查询模型失败",
				"error":   err.Error(),
			})
		}
		return
	}

	// 更新字段
	if req.TokensInputPerQuota != nil {
		model.TokensInputPerQuota = *req.TokensInputPerQuota
	}
	if req.TokensOutputPerQuota != nil {
		model.TokensOutputPerQuota = *req.TokensOutputPerQuota
	}
	if req.MaxTokens != nil {
		model.MaxTokens = *req.MaxTokens
	}
	if req.Temperature != nil {
		model.Temperature = *req.Temperature
	}
	if req.TopP != nil {
		model.TopP = *req.TopP
	}
	model.Enabled = req.Enabled

	err = h.p.UpdateModel(c, model)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新模型失败",
			"error":   err.Error(),
		})
		return
	}

	// 重新查询更新后的数据
	h.db.Where("provider = ? AND model_name = ?", providerName, modelName).First(&model)

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "更新成功",
		"data":    model,
	})
}

// UpdateProviderModelStatus 更新供应商模型状态
func (h *ModelsHandler) UpdateProviderModelStatus(c *gin.Context) {
	providerName := c.Param("provider")
	modelName := c.Param("model")

	var req UpdateModelStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 查找模型
	var model models.GWModelConfig
	err := h.db.Where("provider = ? AND model_name = ?", providerName, modelName).First(&model).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "模型不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "查询模型失败",
				"error":   err.Error(),
			})
		}
		return
	}
	model.Enabled = req.Enabled
	err = h.p.UpdateModel(c, model)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新状态失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "状态更新成功",
	})
}

// DeleteProviderModel 删除供应商模型
func (h *ModelsHandler) DeleteProviderModel(c *gin.Context) {
	providerName := c.Param("provider")
	modelName := c.Param("model")
	var model models.GWModelConfig
	err := h.db.Where("provider = ? AND model_name = ?", providerName, modelName).First(&model).Error
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "获取模型信息失败",
			"error":   err.Error(),
		})
		return
	}
	// 检查是否存在关联的模型组配置
	var count int64
	h.db.Model(&models.GWModelGroupItem{}).Where("model_id = ?", model.ID).Count(&count)
	if count > 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无法删除，该模型正在被模型组使用",
		})
		return
	}
	err = h.p.DeleteModel(c, model)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新状态失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "删除成功",
	})
}
