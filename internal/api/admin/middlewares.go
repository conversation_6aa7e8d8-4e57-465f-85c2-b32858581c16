package admin

import (
	"net/http"
	"strings"
	"time"

	"goalfy_aigateway/internal/config"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

// AdminAuthMiddleware 管理员认证中间件
func AdminAuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取 Authorization 头
		authHeader := c.Get<PERSON>eader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "缺少认证头",
			})
			c.Abort()
			return
		}

		// 检查 Bearer 前缀
		tokenString := strings.TrimPrefix(authHeader, "Bearer ")
		if tokenString == authHeader {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "无效的认证格式",
			})
			c.Abort()
			return
		}

		// 验证 JWT token
		token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			// 验证签名方法
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, jwt.ErrSignatureInvalid
			}
			return []byte(getJWTSecret()), nil
		})

		if err != nil || !token.Valid {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "无效的认证令牌",
			})
			c.Abort()
			return
		}

		// 验证 claims
		if claims, ok := token.Claims.(jwt.MapClaims); ok {
			// 检查过期时间
			if exp, ok := claims["exp"].(float64); ok {
				if time.Now().Unix() > int64(exp) {
					c.JSON(http.StatusUnauthorized, gin.H{
						"success": false,
						"message": "认证令牌已过期",
					})
					c.Abort()
					return
				}
			}

			// 检查是否为管理员
			if role, ok := claims["role"].(string); !ok || role != "admin" {
				c.JSON(http.StatusForbidden, gin.H{
					"success": false,
					"message": "权限不足",
				})
				c.Abort()
				return
			}

			// 将用户信息存储到上下文
			c.Set("admin_user", claims["sub"])
		} else {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "无效的认证令牌格式",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// GenerateAdminToken 生成管理员 JWT token
func GenerateAdminToken() (string, error) {
	// 创建 token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
		"sub":  "admin",
		"role": "admin",
		"iat":  time.Now().Unix(),
		"exp":  time.Now().Add(24 * time.Hour).Unix(), // 24小时过期
	})

	// 签名 token
	tokenString, err := token.SignedString([]byte(getJWTSecret()))
	if err != nil {
		return "", err
	}

	return tokenString, nil
}

// getJWTSecret 获取 JWT 密钥
func getJWTSecret() string {
	cfg := config.GetConfig()
	if cfg.Admin.JWTSecret != "" {
		return cfg.Admin.JWTSecret
	}
	// 如果没有配置，使用默认密钥（生产环境应该配置）
	return "default-jwt-secret-change-in-production"
}
