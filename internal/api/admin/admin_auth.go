package admin

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"goalfy_aigateway/internal/config"
)

// AdminLoginRequest 管理员登录请求
type AdminLoginRequest struct {
	Key string `json:"key" binding:"required"`
}

// AdminLoginResponse 管理员登录响应
type AdminLoginResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
	Token   string `json:"token,omitempty"`
}

// AdminLogin 管理员登录
func AdminLogin(c *gin.Context) {
	var req AdminLoginRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, AdminLoginResponse{
			Success: false,
			Message: "请求参数错误",
		})
		return
	}

	// 获取配置中的管理员密钥
	cfg := config.GetConfig()
	if cfg.Admin.LoginKey == "" {
		c.JSO<PERSON>(http.StatusInternalServerError, AdminLoginResponse{
			Success: false,
			Message: "管理员密钥未配置",
		})
		return
	}

	// 验证密钥
	if req.Key != cfg.Admin.LoginKey {
		c.JSON(http.StatusUnauthorized, AdminLoginResponse{
			Success: false,
			Message: "密钥错误",
		})
		return
	}

	// 生成 JWT token
	token, err := GenerateAdminToken()
	if err != nil {
		c.JSON(http.StatusInternalServerError, AdminLoginResponse{
			Success: false,
			Message: "生成认证令牌失败",
		})
		return
	}

	c.JSON(http.StatusOK, AdminLoginResponse{
		Success: true,
		Token:   token,
	})
}
