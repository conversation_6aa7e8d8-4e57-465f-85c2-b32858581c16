package api

import (
	"goalfy_aigateway/internal/api/admin"
	"goalfy_aigateway/internal/api/v1"
	"goalfy_aigateway/internal/services/adapters"
	"goalfy_aigateway/internal/services/router"
	"goalfy_aigateway/internal/storage"
	"goalfy_aigateway/pkg/logger"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

// Run 启动Gin服务
func Run(addr string) error {
	engine := gin.New()
	engine.ContextWithFallback = true
	db, err := storage.Open()
	if err != nil {
		return err
	}
	logger.Info("database initialized")
	providerService := adapters.NewProviderService(db)
	modelRouter := router.NewWeightedModelRouter(db)

	// 设置API路由
	admin.SetupRoutes(engine.Group("/aigateway-admin/api"), db, modelRouter, providerService)
	v1.SetupRoutes(engine.Group("v1"), modelRouter, providerService)

	// 健康检查接口
	engine.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	// 配置admin UI静态文件服务
	// 静态资源文件（CSS、JS等）
	engine.StaticFS("/aigateway-admin/assets", http.Dir("./ui/dist/assets"))

	// admin UI的SPA路由支持 - 必须放在最后
	engine.NoRoute(func(c *gin.Context) {
		// 只有以/admin开头且不是API路径的请求才返回index.html
		path := c.Request.URL.Path

		// 如果是admin UI路径但不是API路径，返回index.html
		if strings.HasPrefix(path, "/aigateway-admin/") &&
			!strings.HasPrefix(path, "/aigateway-admin/api") &&
			!strings.HasPrefix(path, "/aigateway-admin/assets") {
			c.File("./ui/dist/index.html")
		} else if path == "/aigateway-admin" {
			// 重定向 /admin 到 /admin/
			c.Redirect(http.StatusMovedPermanently, "/aigateway-admin/")
		} else {
			c.JSON(http.StatusNotFound, gin.H{"error": "Not Found"})
		}
	})

	return engine.Run(addr)
}
