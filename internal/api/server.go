package api

import (
	"goalfy_aigateway/internal/api/admin"
	"goalfy_aigateway/internal/api/v1"
	"goalfy_aigateway/internal/services/adapters"
	"goalfy_aigateway/internal/services/router"
	"goalfy_aigateway/internal/storage"
	"goalfy_aigateway/pkg/logger"
	"net/http"

	"github.com/gin-gonic/gin"
)

// Run 启动Gin服务
func Run(addr string) error {
	engine := gin.New()
	engine.ContextWithFallback = true
	db, err := storage.Open()
	if err != nil {
		return err
	}
	logger.Info("database initialized")
	providerService := adapters.NewProviderService(db)
	modelRouter := router.NewWeightedModelRouter(db)
	// 2. 最后定义静态文件路由，使用特定前缀避免冲突
	// 例如使用 /admin/static/*filepath 而不是 /admin/*filepath
	engine.StaticFS("/static", http.Dir("./web/admin/static"))

	// 如果需要提供单页应用(SPA)支持
	engine.GET("/app/*filepath", func(c *gin.Context) {
		// 所有匹配不到具体API的请求都返回index.html
		c.File("./web/admin/index.html")
	})
	admin.SetupRoutes(engine.Group("/admin"), db, modelRouter, providerService)
	v1.SetupRoutes(engine.Group("v1"), modelRouter, providerService)

	// 健康检查接口
	engine.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	return engine.Run(addr)
}
