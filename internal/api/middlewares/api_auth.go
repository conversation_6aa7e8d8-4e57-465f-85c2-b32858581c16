package middlewares

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"goalfy_aigateway/pkg/utils"
	"net/http"
	"strconv"
	"strings"
	"time"
)

var ctxAuthInfoKey = "auth-info"

type ApiAuthInfo struct {
	Grp  string
	User string
	Exp  int64
}

func ApiAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取 Authorization 头
		authHeader := c.<PERSON>("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "缺少认证头",
			})
			c.Abort()
			return
		}
		// 验证 JWT token
		token, err := decodeApiKey(strings.TrimPrefix(authHeader, "Bearer sk-v1"))
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "无效的认证格式",
			})
			c.Abort()
			return
		}
		// 验证 claims

		if time.Now().Unix() > int64(token.Exp) {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "认证令牌已过期",
			})
			c.Abort()
			return
		}
		c.Set(ctxAuthInfoKey, *token)
		c.Next()
	}
}

var apiKeySignBytes = []byte{
	0x05, 0x43, 0x00, 0x01, 0x01, 0x93, 0x56, 0x11,
	0x05, 0x43, 0x05, 0x01, 0x01, 0x93, 0x56, 0x11,
}

func GenerateApiKey(i ApiAuthInfo, expireDays int) (string, error) {
	// 创建 token
	expireTime := time.Now().AddDate(0, 0, expireDays)
	data := fmt.Sprintf("%s|%s|%d", i.Grp, i.User, expireTime.Unix())
	key, err := utils.AesEncode(data, apiKeySignBytes)
	return "sk-v1" + key, err
}

func decodeApiKey(tokenString string) (*ApiAuthInfo, error) {
	data, err := utils.AesDecode(tokenString, apiKeySignBytes)
	if err != nil {
		return nil, err
	}
	var info ApiAuthInfo
	values := strings.Split(data, "|")
	if len(values) != 3 {
		return nil, fmt.Errorf("unexcepted data: %s ", data)
	}
	info.Grp = values[0]
	info.User = values[1]
	exp, err := strconv.ParseInt(values[2], 10, 64)
	if err != nil {
		return nil, err
	}
	info.Exp = exp
	return &info, nil
}
