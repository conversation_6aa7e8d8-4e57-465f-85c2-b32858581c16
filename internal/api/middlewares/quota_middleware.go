package middlewares

import (
	"goalfy_aigateway/internal/services/quota"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

// QuotaMiddleware 配额检查中间件
type QuotaMiddleware struct {
	manager *quota.QuotaLimiterManager
}

// NewQuotaMiddleware 创建配额中间件
func NewQuotaMiddleware(manager *quota.QuotaLimiterManager) *QuotaMiddleware {
	return &QuotaMiddleware{
		manager: manager,
	}
}

// QuotaCheckMiddleware 配额检查中间件
func (m *QuotaMiddleware) QuotaCheckMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头或上下文中获取用户信息
		userID := c.GetHeader("X-User-ID")
		group := c.GetHeader("X-Group")

		// 如果没有提供组信息，使用默认组
		if group == "" {
			group = "default"
		}

		// 如果没有用户ID，拒绝请求
		if userID == "" {
			c.<PERSON><PERSON>(http.StatusUnauthorized, gin.H{
				"error": "Missing user ID",
				"code":  "MISSING_USER_ID",
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中，供后续处理使用
		c.Set("user_id", userID)
		c.Set("group", group)

		c.Next()
	}
}

// PreQuotaCheckMiddleware 预检查配额中间件（在解析请求参数后调用）
func (m *QuotaMiddleware) PreQuotaCheckMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "User ID not found in context",
				"code":  "INTERNAL_ERROR",
			})
			c.Abort()
			return
		}

		group, exists := c.Get("group")
		if !exists {
			group = "default"
		}

		// 从上下文中获取模型信息（需要在路由处理中设置）
		provider, _ := c.Get("provider")
		model, _ := c.Get("model")
		inputTokens, _ := c.Get("input_tokens")
		outputTokens, _ := c.Get("output_tokens")

		// 如果模型信息不完整，跳过预检查
		if provider == nil || model == nil {
			c.Next()
			return
		}

		// 执行配额和限速检查
		req := &quota.CheckRequest{
			UserID:       userID.(string),
			Group:        group.(string),
			Provider:     provider.(string),
			Model:        model.(string),
			InputTokens:  getIntValue(inputTokens),
			OutputTokens: getIntValue(outputTokens),
		}

		result, err := m.manager.CheckAndReserve(req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Quota check failed",
				"code":    "QUOTA_CHECK_ERROR",
				"details": err.Error(),
			})
			c.Abort()
			return
		}

		if !result.Allowed {
			// 设置重试头
			if result.RetryAfter > 0 {
				c.Header("Retry-After", strconv.Itoa(int(result.RetryAfter.Seconds())))
			}

			// 根据不同的错误类型返回不同的状态码
			statusCode := http.StatusTooManyRequests
			errorCode := "RATE_LIMIT_EXCEEDED"

			if !result.QuotaAllowed {
				statusCode = http.StatusPaymentRequired
				errorCode = "QUOTA_EXCEEDED"
			}

			c.JSON(statusCode, gin.H{
				"error":       result.Message,
				"code":        errorCode,
				"quota_cost":  result.QuotaCost,
				"retry_after": result.RetryAfter.Seconds(),
				"details": gin.H{
					"quota_allowed":   result.QuotaAllowed,
					"limiter_allowed": result.LimiterAllowed,
				},
			})
			c.Abort()
			return
		}

		// 将检查结果存储到上下文中
		c.Set("quota_check_result", result)
		c.Next()
	}
}

// PostQuotaConsumeMiddleware 配额消费中间件（在请求成功后调用）
func (m *QuotaMiddleware) PostQuotaConsumeMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 只有在请求成功时才消费配额
		if c.Writer.Status() >= 200 && c.Writer.Status() < 300 {
			userID, _ := c.Get("user_id")
			group, _ := c.Get("group")
			provider, _ := c.Get("provider")
			model, _ := c.Get("model")
			inputTokens, _ := c.Get("actual_input_tokens")   // 实际使用的输入token
			outputTokens, _ := c.Get("actual_output_tokens") // 实际使用的输出token

			if userID != nil && group != nil && provider != nil && model != nil {
				req := &quota.CheckRequest{
					UserID:       userID.(string),
					Group:        group.(string),
					Provider:     provider.(string),
					Model:        model.(string),
					InputTokens:  getIntValue(inputTokens),
					OutputTokens: getIntValue(outputTokens),
				}

				// 异步消费配额，避免影响响应时间
				go func() {
					err := m.manager.ConsumeQuota(req)
					if err != nil {
						// 记录错误日志，但不影响响应
						// logger.Error("Failed to consume quota", zap.Error(err))
					}
				}()
			}
		}
	}
}

// QuotaStatusHandler 获取配额状态的处理器
func (m *QuotaMiddleware) QuotaStatusHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.GetHeader("X-User-ID")
		group := c.GetHeader("X-Group")

		if group == "" {
			group = "default"
		}

		if userID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Missing user ID",
				"code":  "MISSING_USER_ID",
			})
			return
		}

		status, err := m.manager.GetStatus(userID, group)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to get quota status",
				"code":    "QUOTA_STATUS_ERROR",
				"details": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"user_id":   userID,
			"group":     group,
			"status":    status,
			"timestamp": time.Now().Unix(),
		})
	}
}

// AdminResetQuotaHandler 管理员重置配额的处理器
func (m *QuotaMiddleware) AdminResetQuotaHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		group := c.Param("group")
		if group == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Missing group parameter",
				"code":  "MISSING_GROUP",
			})
			return
		}

		err := m.manager.ResetQuota(group)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to reset quota",
				"code":    "QUOTA_RESET_ERROR",
				"details": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message":   "Quota reset successfully",
			"group":     group,
			"timestamp": time.Now().Unix(),
		})
	}
}

// getIntValue 安全地获取整数值
func getIntValue(value interface{}) int {
	if value == nil {
		return 0
	}

	switch v := value.(type) {
	case int:
		return v
	case int64:
		return int(v)
	case float64:
		return int(v)
	case string:
		if i, err := strconv.Atoi(v); err == nil {
			return i
		}
	}

	return 0
}
