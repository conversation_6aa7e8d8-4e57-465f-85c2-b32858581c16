package adapters

import (
	"context"
	"goalfy_aigateway/internal/models"
	"goalfy_aigateway/internal/proto"
)

type AIClientAdapter interface {
	ChatCompletion(ctx context.Context, req *proto.ChatCompletionRequest, model *models.GWModelConfig) (*proto.ChatCompletionResponse, error)
}

type MockAIClient struct{}

func (c *MockAIClient) ChatCompletion(ctx context.Context, req *proto.ChatCompletionRequest, model *models.GWModelConfig) (*proto.ChatCompletionResponse, error) {
	resp := proto.ChatCompletionResponse{
		ID:      "chatcmpl-mockid",
		Object:  "chat.completion",
		Created: 1710000000,
		Model:   req.Model,
		Choices: []proto.ChatCompletionChoice{
			{
				Index: 0,
				Message: proto.ChatMessage{
					Role:    "assistant",
					Content: "这是一个mock回复。",
					ToolCalls: []proto.ToolCall{
						{
							ID:       "tool_call_id",
							Type:     "function",
							Function: proto.FunctionCall{Name: "function_name", Arguments: `{"id":123}`},
						},
					},
				},

				FinishReason: "tool_calls",
			},
		},
		Usage: &proto.ChatCompletionUsage{
			PromptTokens:     10,
			CompletionTokens: 5,
			TotalTokens:      15,
		},
	}
	return &resp, nil
}
