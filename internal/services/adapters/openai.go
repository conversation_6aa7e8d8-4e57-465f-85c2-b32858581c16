package adapters

import (
	"bytes"
	"context"
	"fmt"
	jsoniter "github.com/json-iterator/go"
	"goalfy_aigateway/internal/models"
	"goalfy_aigateway/internal/proto"
	"io"
	"net/http"
)

type OpenAIClient struct {
	httpClient *http.Client
	baseUrl    string
	apiKey     string
}

func (c *OpenAIClient) ChatCompletion(ctx context.Context, req *proto.ChatCompletionRequest, model *models.GWModelConfig) (*proto.ChatCompletionResponse, error) {
	reqModel := req.Model
	req.Model = model.ModelName
	reqBytes, err := jsoniter.Marshal(req)
	if err != nil {
		return nil, err
	}
	req.Model = reqModel

	httpReq, err := http.NewRequest("POST", c.baseUrl+"/chat/completions", bytes.NewReader(reqBytes))
	if err != nil {
		return nil, err
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", c.apiKey))
	httpResp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, err
	}

	data, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, err
	}
	if httpResp.StatusCode != http.StatusOK {
		var errResp proto.ErrorResponse
		if err := jsoniter.Unmarshal(data, &errResp); err != nil {
			return nil, fmt.Errorf("请求失败: HTTP %d", httpResp.StatusCode)
		}
		return nil, &errResp
	}

	resp := &proto.ChatCompletionResponse{}
	err = jsoniter.Unmarshal(data, resp)
	if err != nil {
		return nil, err
	}
	resp.Raw = data
	return resp, nil
}
