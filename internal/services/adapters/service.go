package adapters

import (
	"context"
	"goalfy_aigateway/pkg/utils"

	"fmt"
	"goalfy_aigateway/internal/models"
	"goalfy_aigateway/internal/storage"
	"goalfy_aigateway/pkg/logger"
	"net/http"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
)

// 注意：实际使用中密钥应该从安全的地方获取，而不是硬编码
const aesKeyV1 = "1234567890123456" // 16字节，符合AES-128要求

// ProviderService AI客户端工厂
type ProviderService struct {
	clients    map[string]AIClientAdapter
	lock       sync.RWMutex
	httpClient *http.Client
	dao        *storage.ProviderStorage
}

// NewProviderService 创建新的AI客户端工厂
func NewProviderService(db storage.CommonDao) *ProviderService {
	httpClient := &http.Client{
		Timeout: 180 * time.Second,
	}
	f := &ProviderService{
		clients:    make(map[string]AIClientAdapter),
		httpClient: httpClient,
		dao:        storage.NewProviderStorage(db),
	}

	// 预加载所有活跃的提供商
	providers, err := f.dao.GetProviders(nil)
	if err != nil {
		logger.Fatal("failed to load providers from database", zap.Error(err))
	}

	var providerNames []string
	for _, p := range providers {
		cli, err := f.createClient(p)
		if err != nil {
			logger.Warn("get provider failed", zap.String("provider", p.Name), zap.Error(err))
			continue
		}
		providerNames = append(providerNames, p.Name)
		f.clients[p.Name] = cli
	}
	logger.Info("providers loaded from database", zap.Strings("providers", providerNames))
	return f
}

func (f *ProviderService) ListAdapter() map[string]AIClientAdapter {
	f.lock.RLock()
	defer f.lock.RUnlock()
	adapters := make(map[string]AIClientAdapter)
	for name, cli := range f.clients {
		adapters[name] = cli
	}
	return adapters
}

func (f *ProviderService) AddProvider(ctx context.Context, provider models.GWProvider) error {
	encodeKey, err := utils.AesEncode(provider.ApiKey, []byte(aesKeyV1))
	if err != nil {
		return err
	}
	provider.ApiKey = "v1:" + encodeKey
	cli, err := f.createClient(provider)
	if err != nil {
		return err
	}
	err = f.dao.Create(ctx, &provider)
	if err != nil {
		return err
	}
	f.lock.Lock()
	defer f.lock.Unlock()
	f.clients[provider.Name] = cli
	return nil
}

func (f *ProviderService) UpdateProvider(ctx context.Context, provider models.GWProvider) error {
	keys := strings.SplitN(provider.ApiKey, ":", 2)
	if len(keys) != 2 {
		apiKeyStr, err := utils.AesEncode(provider.ApiKey, []byte(aesKeyV1))
		if err != nil {
			return err
		}
		provider.ApiKey = "v1:" + apiKeyStr
	}
	cli, err := f.createClient(provider)
	if err != nil {
		return err
	}
	err = f.dao.Save(ctx, provider)
	if err != nil {
		return err
	}
	f.lock.Lock()
	defer f.lock.Unlock()
	f.clients[provider.Name] = cli
	return nil
}

func (f *ProviderService) DeleteProvider(ctx context.Context, provider models.GWProvider) error {
	err := f.dao.Delete(ctx, &provider)
	if err != nil {
		return err
	}
	f.lock.Lock()
	defer f.lock.Unlock()
	delete(f.clients, provider.Name)
	return nil
}

func (f *ProviderService) createClient(provider models.GWProvider) (AIClientAdapter, error) {
	key, err := utils.AesDecode(strings.TrimPrefix(provider.ApiKey, "v1:"), []byte(aesKeyV1))
	if err != nil {
		return nil, err
	}
	var newClient AIClientAdapter
	switch provider.ApiType {
	case "openai":
		newClient = &OpenAIClient{
			httpClient: f.httpClient,
			baseUrl:    strings.TrimSuffix(provider.BaseUrl, "/"),
			apiKey:     key,
		}
	case "claude":
		newClient = NewClaudeClient(provider.BaseUrl, key)
	case "mock":
		newClient = &MockAIClient{}
	default:
		return nil, fmt.Errorf("不支持的供应商类型: %s", provider.ApiType)
	}
	return newClient, nil
}

// GetClient 获取AI客户端
func (f *ProviderService) GetClient(providerName string) (AIClientAdapter, error) {
	// 先检查缓存
	f.lock.RLock()
	client, exists := f.clients[providerName]
	f.lock.RUnlock()
	if !exists {
		return nil, fmt.Errorf("provider %s not exists", providerName)
	}
	return client, nil
}
