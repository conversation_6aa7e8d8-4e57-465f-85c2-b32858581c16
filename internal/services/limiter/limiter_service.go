package limiter

import (
	"fmt"
	"sync"
	"time"

	"goalfy_aigateway/internal/storage"
	"golang.org/x/time/rate"
)

// RateLimiterService 限速服务
type RateLimiterService struct {
	storage       *storage.LimiterStorage
	userLimiters  map[string]*rate.Limiter
	groupLimiters map[string]*rate.Limiter
	mutex         sync.RWMutex
}

// NewRateLimiterService 创建限速服务
func NewRateLimiterService(db *storage.CommonDao) *RateLimiterService {
	return &RateLimiterService{
		storage:       storage.NewLimiterStorage(db),
		userLimiters:  make(map[string]*rate.Limiter),
		groupLimiters: make(map[string]*rate.Limiter),
	}
}

// RateLimitRequest 限速检查请求
type RateLimitRequest struct {
	UserID    string
	Group     string
	Provider  string
	Model     string
	QuotaCost float64 // 本次请求的配额消耗
}

// RateLimitResult 限速检查结果
type RateLimitResult struct {
	Allowed      bool
	UserAllowed  bool
	GroupAllowed bool
	Message      string
	RetryAfter   time.Duration
}

// CheckRateLimit 检查限速
func (s *RateLimiterService) CheckRateLimit(req *RateLimitRequest) (*RateLimitResult, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	// 1. 检查用户限速
	userAllowed, userRetryAfter, err := s.checkUserRateLimit(req)
	if err != nil {
		return nil, err
	}

	// 2. 检查组限速
	groupAllowed, groupRetryAfter, err := s.checkGroupRateLimit(req)
	if err != nil {
		return nil, err
	}

	// 3. 综合判断
	allowed := userAllowed && groupAllowed
	var retryAfter time.Duration
	var message string

	if !allowed {
		if !userAllowed && !groupAllowed {
			message = "用户和组限速均超限"
			retryAfter = maxDuration(userRetryAfter, groupRetryAfter)
		} else if !userAllowed {
			message = "用户限速超限"
			retryAfter = userRetryAfter
		} else {
			message = "组限速超限"
			retryAfter = groupRetryAfter
		}
	} else {
		message = "限速检查通过"
	}

	return &RateLimitResult{
		Allowed:      allowed,
		UserAllowed:  userAllowed,
		GroupAllowed: groupAllowed,
		Message:      message,
		RetryAfter:   retryAfter,
	}, nil
}

// checkUserRateLimit 检查用户限速
func (s *RateLimiterService) checkUserRateLimit(req *RateLimitRequest) (bool, time.Duration, error) {
	// 获取用户限速配置
	limitConfig, err := s.storage.GetUserLimitConfig(req.UserID, req.Group)
	if err != nil {
		return false, 0, err
	}

	// 如果配置为 -1，表示不限制
	if limitConfig.QueryLimit < 0 && limitConfig.TotalLimit < 0 {
		return true, 0, nil
	}

	userKey := fmt.Sprintf("user:%s:%s", req.UserID, req.Group)

	// 检查单次查询限制
	if limitConfig.QueryLimit >= 0 && req.QuotaCost > limitConfig.QueryLimit {
		return false, time.Minute, nil // 单次查询超限，建议1分钟后重试
	}

	// 检查总限速
	if limitConfig.TotalLimit >= 0 {
		limiter := s.getUserLimiter(userKey, limitConfig.TotalLimit)
		if !limiter.AllowN(time.Now(), int(req.QuotaCost)) {
			// 计算重试时间
			reservation := limiter.ReserveN(time.Now(), int(req.QuotaCost))
			retryAfter := reservation.Delay()
			reservation.Cancel() // 取消预约
			return false, retryAfter, nil
		}
	}

	return true, 0, nil
}

// checkGroupRateLimit 检查组限速
func (s *RateLimiterService) checkGroupRateLimit(req *RateLimitRequest) (bool, time.Duration, error) {
	// 获取组限速配置
	limitConfig, err := s.storage.GetGroupLimitConfig(req.Group)
	if err != nil {
		return false, 0, err
	}

	// 如果配置为 -1，表示不限制
	if limitConfig.QueryLimit < 0 && limitConfig.TotalLimit < 0 {
		return true, 0, nil
	}

	groupKey := fmt.Sprintf("group:%s", req.Group)

	// 检查单次查询限制
	if limitConfig.QueryLimit >= 0 && req.QuotaCost > limitConfig.QueryLimit {
		return false, time.Minute, nil // 单次查询超限，建议1分钟后重试
	}

	// 检查总限速
	if limitConfig.TotalLimit >= 0 {
		limiter := s.getGroupLimiter(groupKey, limitConfig.TotalLimit)
		if !limiter.AllowN(time.Now(), int(req.QuotaCost)) {
			// 计算重试时间
			reservation := limiter.ReserveN(time.Now(), int(req.QuotaCost))
			retryAfter := reservation.Delay()
			reservation.Cancel() // 取消预约
			return false, retryAfter, nil
		}
	}

	return true, 0, nil
}

// getUserLimiter 获取或创建用户限速器
func (s *RateLimiterService) getUserLimiter(key string, limit float64) *rate.Limiter {
	if limiter, exists := s.userLimiters[key]; exists {
		return limiter
	}

	// 创建新的限速器，每秒允许 limit 个配额，突发容量为 limit*2
	limiter := rate.NewLimiter(rate.Limit(limit), int(limit*2))
	s.userLimiters[key] = limiter
	return limiter
}

// getGroupLimiter 获取或创建组限速器
func (s *RateLimiterService) getGroupLimiter(key string, limit float64) *rate.Limiter {
	if limiter, exists := s.groupLimiters[key]; exists {
		return limiter
	}

	// 创建新的限速器，每秒允许 limit 个配额，突发容量为 limit*2
	limiter := rate.NewLimiter(rate.Limit(limit), int(limit*2))
	s.groupLimiters[key] = limiter
	return limiter
}

// CleanupLimiters 清理不活跃的限速器（定期调用）
func (s *RateLimiterService) CleanupLimiters() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 简单的清理策略：清空所有限速器，让它们重新创建
	// 在生产环境中，可以实现更复杂的LRU清理策略
	s.userLimiters = make(map[string]*rate.Limiter)
	s.groupLimiters = make(map[string]*rate.Limiter)
}

// UpdateLimitConfig 更新限速配置
func (s *RateLimiterService) UpdateLimitConfig(userID, group string, totalLimit, queryLimit float64) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 更新数据库配置
	err := s.storage.UpdateUserLimitConfig(userID, group, totalLimit, queryLimit)
	if err != nil {
		return fmt.Errorf("更新限速配置失败: %w", err)
	}

	// 清理相关的限速器，让它们重新创建
	userKey := fmt.Sprintf("user:%s:%s", userID, group)
	delete(s.userLimiters, userKey)

	return nil
}

// maxDuration 返回两个时间间隔中的较大值
func maxDuration(a, b time.Duration) time.Duration {
	if a > b {
		return a
	}
	return b
}
