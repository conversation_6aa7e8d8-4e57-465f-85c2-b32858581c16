package quota

import (
	"fmt"
	"goalfy_aigateway/internal/services/limiter"
	"time"

	"goalfy_aigateway/internal/storage"
)

// QuotaLimiterManager 配额和限速统一管理器
type QuotaLimiterManager struct {
	quotaService   *QuotaService
	limiterService *limiter.RateLimiterService
}

// NewQuotaLimiterManager 创建配额限速管理器
func NewQuotaLimiterManager(db *storage.CommonDao) *QuotaLimiterManager {
	return &QuotaLimiterManager{
		quotaService:   NewQuotaService(db),
		limiterService: limiter.NewRateLimiterService(db),
	}
}

// CheckRequest 检查请求的配额和限速
type CheckRequest struct {
	UserID       string
	Group        string
	Provider     string
	Model        string
	InputTokens  int
	OutputTokens int
}

// CheckResult 检查结果
type CheckResult struct {
	Allowed        bool
	QuotaAllowed   bool
	LimiterAllowed bool
	QuotaCost      float64
	Message        string
	RetryAfter     time.Duration
	QuotaStatus    *QuotaCheckResult
	LimiterStatus  *limiter.RateLimitResult
}

// CheckAndReserve 检查配额和限速，如果通过则预留资源
func (m *QuotaLimiterManager) CheckAndReserve(req *CheckRequest) (*CheckResult, error) {
	// 1. 检查配额
	quotaReq := &CheckQuotaRequest{
		UserID:       req.UserID,
		Group:        req.Group,
		Provider:     req.Provider,
		Model:        req.Model,
		InputTokens:  req.InputTokens,
		OutputTokens: req.OutputTokens,
	}

	quotaResult, err := m.quotaService.CheckQuota(quotaReq)
	if err != nil {
		return nil, fmt.Errorf("配额检查失败: %w", err)
	}

	// 2. 检查限速
	limiterReq := &limiter.RateLimitRequest{
		UserID:    req.UserID,
		Group:     req.Group,
		Provider:  req.Provider,
		Model:     req.Model,
		QuotaCost: quotaResult.QuotaCost,
	}

	limiterResult, err := m.limiterService.CheckRateLimit(limiterReq)
	if err != nil {
		return nil, fmt.Errorf("限速检查失败: %w", err)
	}

	// 3. 综合判断
	allowed := quotaResult.Allowed && limiterResult.Allowed
	var message string
	var retryAfter time.Duration

	if !allowed {
		if !quotaResult.Allowed && !limiterResult.Allowed {
			message = fmt.Sprintf("配额和限速均不通过: %s, %s", quotaResult.Message, limiterResult.Message)
			retryAfter = limiterResult.RetryAfter
		} else if !quotaResult.Allowed {
			message = fmt.Sprintf("配额不足: %s", quotaResult.Message)
			retryAfter = time.Hour // 配额不足建议1小时后重试
		} else {
			message = fmt.Sprintf("限速超限: %s", limiterResult.Message)
			retryAfter = limiterResult.RetryAfter
		}
	} else {
		message = "检查通过"
	}

	return &CheckResult{
		Allowed:        allowed,
		QuotaAllowed:   quotaResult.Allowed,
		LimiterAllowed: limiterResult.Allowed,
		QuotaCost:      quotaResult.QuotaCost,
		Message:        message,
		RetryAfter:     retryAfter,
		QuotaStatus:    quotaResult,
		LimiterStatus:  limiterResult,
	}, nil
}

// ConsumeQuota 消费配额（在请求成功后调用）
func (m *QuotaLimiterManager) ConsumeQuota(req *CheckRequest) error {
	quotaReq := &CheckQuotaRequest{
		UserID:       req.UserID,
		Group:        req.Group,
		Provider:     req.Provider,
		Model:        req.Model,
		InputTokens:  req.InputTokens,
		OutputTokens: req.OutputTokens,
	}

	return m.quotaService.ConsumeQuota(quotaReq)
}

// GetStatus 获取用户和组的配额状态
func (m *QuotaLimiterManager) GetStatus(userID, group string) (map[string]interface{}, error) {
	return m.quotaService.GetQuotaStatus(userID, group)
}

// ResetQuota 重置配额
func (m *QuotaLimiterManager) ResetQuota(group string) error {
	return m.quotaService.ResetQuota(group)
}

// UpdateLimitConfig 更新限速配置
func (m *QuotaLimiterManager) UpdateLimitConfig(userID, group string, totalLimit, queryLimit float64) error {
	return m.limiterService.UpdateLimitConfig(userID, group, totalLimit, queryLimit)
}

// CleanupLimiters 清理限速器
func (m *QuotaLimiterManager) CleanupLimiters() {
	m.limiterService.CleanupLimiters()
}

// StartCleanupRoutine 启动定期清理例程
func (m *QuotaLimiterManager) StartCleanupRoutine() {
	go func() {
		ticker := time.NewTicker(1 * time.Hour) // 每小时清理一次
		defer ticker.Stop()

		for range ticker.C {
			m.CleanupLimiters()
		}
	}()
}

// QuotaMiddleware 配额检查中间件函数
func (m *QuotaLimiterManager) QuotaMiddleware() func(userID, group, provider, model string, inputTokens, outputTokens int) (*CheckResult, error) {
	return func(userID, group, provider, model string, inputTokens, outputTokens int) (*CheckResult, error) {
		req := &CheckRequest{
			UserID:       userID,
			Group:        group,
			Provider:     provider,
			Model:        model,
			InputTokens:  inputTokens,
			OutputTokens: outputTokens,
		}

		return m.CheckAndReserve(req)
	}
}

// ConsumeMiddleware 配额消费中间件函数
func (m *QuotaLimiterManager) ConsumeMiddleware() func(userID, group, provider, model string, inputTokens, outputTokens int) error {
	return func(userID, group, provider, model string, inputTokens, outputTokens int) error {
		req := &CheckRequest{
			UserID:       userID,
			Group:        group,
			Provider:     provider,
			Model:        model,
			InputTokens:  inputTokens,
			OutputTokens: outputTokens,
		}

		return m.ConsumeQuota(req)
	}
}
