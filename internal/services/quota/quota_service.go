package quota

import (
	"fmt"
	"sync"
	"time"

	"goalfy_aigateway/internal/models"
	"goalfy_aigateway/internal/storage"
)

// QuotaService 配额管理服务
type QuotaService struct {
	storage  *storage.QuotaStorage
	modelMap map[[2]string]*models.GWModelConfig
	cache    *QuotaCache
	mutex    sync.RWMutex
}

// QuotaCache 配额缓存
type QuotaCache struct {
	userQuotas  map[string]*models.GWUserQuotaConfig
	groupQuotas map[string]*models.GWGroupQuotaConfig
	mutex       sync.RWMutex
	ttl         time.Duration
	lastUpdate  map[string]time.Time
}

// NewQuotaService 创建配额服务
func NewQuotaService(db *storage.CommonDao) *QuotaService {
	cache := &QuotaCache{
		userQuotas:  make(map[string]*models.GWUserQuotaConfig),
		groupQuotas: make(map[string]*models.GWGroupQuotaConfig),
		ttl:         5 * time.Minute, // 缓存5分钟
		lastUpdate:  make(map[string]time.Time),
	}

	return &QuotaService{
		storage:  storage.NewQuotaStorage(db),
		modelMap: make(map[[2]string]*models.GWModelConfig),
		cache:    cache,
	}
}

// CheckQuotaRequest 配额检查请求
type CheckQuotaRequest struct {
	UserID       string
	Group        string
	Provider     string
	Model        string
	InputTokens  int
	OutputTokens int
}

// QuotaCheckResult 配额检查结果
type QuotaCheckResult struct {
	Allowed        bool
	QuotaCost      float64
	UserRemaining  float64
	GroupRemaining float64
	Message        string
}

// CheckQuota 检查用户和组的配额
func (s *QuotaService) CheckQuota(req *CheckQuotaRequest) (*QuotaCheckResult, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	modelConfig, ok := s.modelMap[[2]string{req.Provider, req.Model}]
	if !ok {
		// 1. 获取模型配置，计算配额消耗
		modelConfig, err := s.storage.GetModelConfig(req.Provider, req.Model)
		if err != nil {
			return nil, fmt.Errorf("模型配置未找到: %s/%s", req.Provider, req.Model)
		}
		s.modelMap[[2]string{req.Provider, req.Model}] = modelConfig
	}

	// 计算配额消耗
	quotaCost := float64(req.InputTokens)/modelConfig.TokensInputPerQuota +
		float64(req.OutputTokens)/modelConfig.TokensOutputPerQuota

	// 2. 检查用户配额
	userQuota, err := s.getUserQuotaWithCache(req.UserID, req.Group)
	if err != nil {
		return nil, err
	}

	userRemaining := userQuota.Quota - userQuota.Used
	if userRemaining < quotaCost {
		return &QuotaCheckResult{
			Allowed:       false,
			QuotaCost:     quotaCost,
			UserRemaining: userRemaining,
			Message:       "用户配额不足",
		}, nil
	}

	// 3. 检查组配额
	groupQuota, err := s.getGroupQuotaWithCache(req.Group)
	if err != nil {
		return nil, err
	}

	groupRemaining := groupQuota.Quota - groupQuota.Used
	if groupRemaining < quotaCost {
		return &QuotaCheckResult{
			Allowed:        false,
			QuotaCost:      quotaCost,
			UserRemaining:  userRemaining,
			GroupRemaining: groupRemaining,
			Message:        "组配额不足",
		}, nil
	}

	return &QuotaCheckResult{
		Allowed:        true,
		QuotaCost:      quotaCost,
		UserRemaining:  userRemaining,
		GroupRemaining: groupRemaining,
		Message:        "配额检查通过",
	}, nil
}

// ConsumeQuota 消费配额
func (s *QuotaService) ConsumeQuota(req *CheckQuotaRequest) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 先检查配额
	result, err := s.CheckQuota(req)
	if err != nil {
		return err
	}

	if !result.Allowed {
		return fmt.Errorf("配额不足: %s", result.Message)
	}

	// 更新配额
	err = s.storage.ConsumeQuota(req.UserID, req.Group, result.QuotaCost)
	if err != nil {
		return err
	}

	// 清除缓存，强制下次重新加载
	s.invalidateCache(req.UserID, req.Group)

	return nil
}

// getUserQuotaWithCache 使用缓存获取用户配额
func (s *QuotaService) getUserQuotaWithCache(userID, group string) (*models.GWUserQuotaConfig, error) {
	key := fmt.Sprintf("%s:%s", userID, group)

	s.cache.mutex.RLock()
	if quota, exists := s.cache.userQuotas[key]; exists {
		if lastUpdate, ok := s.cache.lastUpdate[key]; ok {
			if time.Since(lastUpdate) < s.cache.ttl {
				s.cache.mutex.RUnlock()
				return quota, nil
			}
		}
	}
	s.cache.mutex.RUnlock()

	// 缓存未命中或过期，从存储层获取
	quota, err := s.storage.GetUserQuota(userID, group)
	if err != nil {
		return nil, err
	}

	// 更新缓存
	s.cache.mutex.Lock()
	s.cache.userQuotas[key] = quota
	s.cache.lastUpdate[key] = time.Now()
	s.cache.mutex.Unlock()

	return quota, nil
}

// getGroupQuotaWithCache 使用缓存获取组配额
func (s *QuotaService) getGroupQuotaWithCache(group string) (*models.GWGroupQuotaConfig, error) {
	s.cache.mutex.RLock()
	if quota, exists := s.cache.groupQuotas[group]; exists {
		if lastUpdate, ok := s.cache.lastUpdate[group]; ok {
			if time.Since(lastUpdate) < s.cache.ttl {
				s.cache.mutex.RUnlock()
				return quota, nil
			}
		}
	}
	s.cache.mutex.RUnlock()

	// 缓存未命中或过期，从存储层获取
	quota, err := s.storage.GetGroupQuota(group)
	if err != nil {
		return nil, err
	}

	// 更新缓存
	s.cache.mutex.Lock()
	s.cache.groupQuotas[group] = quota
	s.cache.lastUpdate[group] = time.Now()
	s.cache.mutex.Unlock()

	return quota, nil
}

// invalidateCache 清除缓存
func (s *QuotaService) invalidateCache(userID, group string) {
	s.cache.mutex.Lock()
	defer s.cache.mutex.Unlock()

	userKey := fmt.Sprintf("%s:%s", userID, group)
	delete(s.cache.userQuotas, userKey)
	delete(s.cache.lastUpdate, userKey)
	delete(s.cache.groupQuotas, group)
	delete(s.cache.lastUpdate, group)
}

// ResetQuota 重置配额
func (s *QuotaService) ResetQuota(group string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	err := s.storage.ResetQuota(group)
	if err != nil {
		return err
	}

	// 清除相关缓存
	s.cache.mutex.Lock()
	defer s.cache.mutex.Unlock()

	// 清除组缓存
	delete(s.cache.groupQuotas, group)
	delete(s.cache.lastUpdate, group)

	// 清除该组下所有用户缓存
	for key := range s.cache.userQuotas {
		if len(key) > len(group) && key[len(key)-len(group):] == group {
			delete(s.cache.userQuotas, key)
			delete(s.cache.lastUpdate, key)
		}
	}

	return nil
}

// GetQuotaStatus 获取配额状态
func (s *QuotaService) GetQuotaStatus(userID, group string) (map[string]interface{}, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	userQuota, err := s.getUserQuotaWithCache(userID, group)
	if err != nil {
		return nil, err
	}

	groupQuota, err := s.getGroupQuotaWithCache(group)
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"user": map[string]interface{}{
			"quota":      userQuota.Quota,
			"used":       userQuota.Used,
			"remaining":  userQuota.Quota - userQuota.Used,
			"total_used": userQuota.TotalUsed,
		},
		"group": map[string]interface{}{
			"quota":      groupQuota.Quota,
			"used":       groupQuota.Used,
			"remaining":  groupQuota.Quota - groupQuota.Used,
			"total_used": groupQuota.TotalUsed,
		},
	}, nil
}

// ClearCache 清除所有缓存
func (s *QuotaService) ClearCache() {
	s.cache.mutex.Lock()
	defer s.cache.mutex.Unlock()

	s.cache.userQuotas = make(map[string]*models.GWUserQuotaConfig)
	s.cache.groupQuotas = make(map[string]*models.GWGroupQuotaConfig)
	s.cache.lastUpdate = make(map[string]time.Time)
}

// StartCacheCleanup 启动缓存清理例程
func (s *QuotaService) StartCacheCleanup() {
	go func() {
		ticker := time.NewTicker(10 * time.Minute) // 每10分钟清理一次过期缓存
		defer ticker.Stop()

		for range ticker.C {
			s.cleanupExpiredCache()
		}
	}()
}

// cleanupExpiredCache 清理过期缓存
func (s *QuotaService) cleanupExpiredCache() {
	s.cache.mutex.Lock()
	defer s.cache.mutex.Unlock()

	now := time.Now()
	for key, lastUpdate := range s.cache.lastUpdate {
		if now.Sub(lastUpdate) > s.cache.ttl {
			delete(s.cache.userQuotas, key)
			delete(s.cache.groupQuotas, key)
			delete(s.cache.lastUpdate, key)
		}
	}
}
