package proto

import "fmt"

// 定义AI网关的请求响应结构；以openai为基础版本；

// 参考官方文档：https://platform.openai.com/docs/api-reference/chat/create

type ErrorResponse struct {
	Err struct {
		Message string `json:"message"`
		Type    string `json:"type"`
		Code    string `json:"code"`
	} `json:"error"`
}

func (e *ErrorResponse) Error() string {
	return fmt.Sprintf("type:%s code:%s message:%s", e.Err.Type, e.Err.Code, e.Err.Message)
}

func Error(err error) *ErrorResponse {
	return &ErrorResponse{
		Err: struct {
			Message string `json:"message"`
			Type    string `json:"type"`
			Code    string `json:"code"`
		}{Message: err.Error(), Type: "error", Code: "1"},
	}
}

// ContentPart 定义消息内容部分（支持文本和图片）
type ContentPart struct {
	Type     string    `json:"type"` // "text" or "image_url"
	Text     *string   `json:"text,omitempty"`
	ImageURL *ImageURL `json:"image_url,omitempty"`
	Cached   bool      `json:"cached,omitempty"`
}

// ImageURL 定义图片URL结构
type ImageURL struct {
	URL    string  `json:"url"`
	Detail *string `json:"detail,omitempty"` // "low", "high", "auto"
}

type ChatMessage struct {
	Role       string      `json:"role" binding:"required"`
	Content    interface{} `json:"content" binding:"required"` // string or []ContentPart
	Name       *string     `json:"name,omitempty"`
	ToolCalls  []ToolCall  `json:"tool_calls,omitempty"`
	ToolCallID *string     `json:"tool_call_id,omitempty"`
	Cached     bool        `json:"cached,omitempty"`
}

// FunctionCall 定义函数调用请求结构
// 兼容OpenAI function call协议

type FunctionCall struct {
	Name      string `json:"name"`
	Arguments string `json:"arguments"`
}

// ToolCall 定义新版tools调用结构（OpenAI 2023-07及以后）
type ToolCall struct {
	ID       string       `json:"id"`
	Type     string       `json:"type"` // "function"
	Function FunctionCall `json:"function"`
}

// ChatCompletionFunction 定义functions参数结构
// 参考：https://platform.openai.com/docs/api-reference/chat/create#functions

type ChatCompletionFunction struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
	Cached      bool                   `json:"cached,omitempty"`
}

// ChatCompletionTool 定义tools参数结构
// 参考：https://platform.openai.com/docs/api-reference/chat/create#tools

type ChatCompletionTool struct {
	Type     string                 `json:"type"` // "function"
	Function ChatCompletionFunction `json:"function"`
}

// ToolChoice 定义tool_choice参数结构
// 参考：https://platform.openai.com/docs/api-reference/chat/create#tool_choice

type ToolChoice struct {
	Type     string `json:"type"` // "function"/"auto"/"none"
	Function *struct {
		Name string `json:"name"`
	} `json:"function,omitempty"`
}

type ChatCompletionRequest struct {
	Model            string                   `json:"model" binding:"required"`
	Messages         []ChatMessage            `json:"messages" binding:"required"`
	Temperature      *float64                 `json:"temperature,omitempty"`
	TopP             *float32                 `json:"top_p,omitempty"`
	N                *int                     `json:"n,omitempty"`
	Stream           *bool                    `json:"stream,omitempty"`
	Stop             interface{}              `json:"stop,omitempty"` // string or []string
	MaxTokens        *int                     `json:"max_tokens,omitempty"`
	PresencePenalty  *float32                 `json:"presence_penalty,omitempty"`
	FrequencyPenalty *float32                 `json:"frequency_penalty,omitempty"`
	LogitBias        map[string]float32       `json:"logit_bias,omitempty"`
	User             *string                  `json:"user,omitempty"`
	Functions        []ChatCompletionFunction `json:"functions,omitempty"`     // 已废弃，使用tools
	FunctionCall     interface{}              `json:"function_call,omitempty"` // 已废弃，使用tool_choice
	Tools            []ChatCompletionTool     `json:"tools,omitempty"`
	ToolChoice       *string                  `json:"tool_choice,omitempty"` // string or object
	Seed             *int                     `json:"seed,omitempty"`
	ResponseFormat   interface{}              `json:"response_format,omitempty"` // object
	ServiceTier      *string                  `json:"service_tier,omitempty"`
	Thinking         *struct {
		Type         string `json:"type"`
		BudgetTokens int    `json:"budget_tokens,omitempty"`
	} `json:"thinking,omitempty"`
}

// ChatCompletionResponse 定义 OpenAI v1/chat/completions 的响应结构

type ChatCompletionChoice struct {
	Index        int         `json:"index"`
	Message      ChatMessage `json:"message"`
	FinishReason string      `json:"finish_reason"`
}

type ChatCompletionUsage struct {
	PromptTokens      int64 `json:"prompt_tokens"`
	CacheCreateTokens int64 `json:"cache_create_tokens"`
	CacheReadTokens   int64 `json:"cache_read_tokens"`
	CompletionTokens  int64 `json:"completion_tokens"`
	TotalTokens       int64 `json:"total_tokens"`
}

type ChatCompletionResponse struct {
	Raw               []byte                 `json:"-"`
	ID                string                 `json:"id"`
	Object            string                 `json:"object"`
	Created           int64                  `json:"created"`
	Model             string                 `json:"model"`
	Choices           []ChatCompletionChoice `json:"choices"`
	Usage             *ChatCompletionUsage   `json:"usage,omitempty"`
	SystemFingerprint *string                `json:"system_fingerprint,omitempty"`
}

// 流式响应相关结构
type ChatCompletionStreamChoice struct {
	Index        int                       `json:"index"`
	Delta        ChatCompletionStreamDelta `json:"delta"`
	FinishReason *string                   `json:"finish_reason"`
}

type ChatCompletionStreamDelta struct {
	Role      *string    `json:"role,omitempty"`
	Content   *string    `json:"content,omitempty"`
	ToolCalls []ToolCall `json:"tool_calls,omitempty"`
}

type ChatCompletionStreamResponse struct {
	ID                string                       `json:"id"`
	Object            string                       `json:"object"`
	Created           int64                        `json:"created"`
	Model             string                       `json:"model"`
	Choices           []ChatCompletionStreamChoice `json:"choices"`
	SystemFingerprint *string                      `json:"system_fingerprint,omitempty"`
}
