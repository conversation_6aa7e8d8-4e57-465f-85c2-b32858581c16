package proto

type RequestTrace struct {
	UserId    string `header:"X-User-ID,default=guest"`
	ProjectId string `header:"X-Project-ID"`
	ChatId    string `header:"X-Chat-ID"`

	RootChatId string `header:"X-Root-Chat-ID"`
	DemandId   string `header:"X-Demand-ID"`
	TaskId     string `header:"X-Task-ID"`

	TraceId  string `header:"X-Trace-ID"`
	SpanId   string `header:"X-Span-ID"`
	ApiGroup string `header:"-"`
}
