package storage

import (
	"context"
	"goalfy_aigateway/internal/models"
	"gorm.io/gorm"
)

// ModelGroupStorage 模型组服务
type ModelGroupStorage struct {
	CommonDao
}

// NewModelGroupStorage 创建新的模型组服务
func NewModelGroupStorage(db CommonDao) *ModelGroupStorage {
	return &ModelGroupStorage{CommonDao: db}
}

// GetModelGroupByName 根据名称获取模型组
func (s *ModelGroupStorage) GetModelGroupByName(name string) (*models.ModelGroupConfig, error) {

	// 使用 JOIN 查询获取模型组项目，并验证模型配置存在且启用
	var items []models.GWModelGroupItem
	var group models.GWModelGroup
	err := s.Where("name = ?", name).First(&group).Error
	if err != nil {
		return nil, err
	}
	err = s.Preload("Model").Where("group_id = ? ", group.ID).Find(&items).Error
	if err != nil {
		return nil, err
	}
	return &models.ModelGroupConfig{
		GWModelGroup: group,
		Models:       items,
	}, nil
}

// GetModelGroups 获取模型组
func (s *ModelGroupStorage) GetModelGroups(where map[string]interface{}) (map[uint]*models.ModelGroupConfig, error) {
	var groups []models.GWModelGroup
	err := s.Where(where).Find(&groups).Error
	if err != nil {
		return nil, err
	}
	group_ids := make([]uint, 0, len(groups))
	for _, group := range groups {
		group_ids = append(group_ids, group.ID)
	}
	var items []models.GWModelGroupItem
	err = s.Debug().Preload("Model").Where("group_id in ?", group_ids).Find(&items).Error
	if err != nil {
		return nil, err
	}
	groupMap := make(map[uint]*models.ModelGroupConfig)
	for _, item := range items {
		group, ok := groupMap[item.GroupID]
		if !ok {
			for _, g := range groups {
				if g.ID == item.GroupID {
					group = &models.ModelGroupConfig{
						GWModelGroup: g,
					}
					groupMap[item.GroupID] = group
					break
				}
			}
		}
		group.Models = append(group.Models, item)
	}

	return groupMap, nil
}

func (s *ModelGroupStorage) CreateGroup(ctx context.Context, group *models.ModelGroupConfig) error {
	err := s.Debug().Transaction(func(tx *gorm.DB) error {
		tx.Create(&group.GWModelGroup)
		for _, model := range group.Models {
			model.GroupID = group.GWModelGroup.ID
			tx.Create(&model)
		}
		return nil
	})
	return err
}

func (s *ModelGroupStorage) SaveGroup(ctx context.Context, group *models.ModelGroupConfig) error {
	err := s.Debug().Transaction(func(tx *gorm.DB) error {
		tx.Save(&group.GWModelGroup)
		for _, model := range group.Models {
			model.GroupID = group.GWModelGroup.ID
			tx.Save(&model)
		}
		return nil
	})
	return err
}
