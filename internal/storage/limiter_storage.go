package storage

import (
	"context"
	"fmt"
	"time"

	"goalfy_aigateway/internal/models"
	"gorm.io/gorm"
)

// LimiterStorage 限速存储层
type LimiterStorage struct {
	db *CommonDao
}

// NewLimiterStorage 创建限速存储层
func NewLimiterStorage(db *CommonDao) *LimiterStorage {
	return &LimiterStorage{db: db}
}

// GetUserLimitConfig 获取用户限速配置
func (s *LimiterStorage) GetUserLimitConfig(userID, group string) (*models.GWUserLimitConfig, error) {
	var limitConfig models.GWUserLimitConfig
	err := s.db.Where("user_id = ? AND group = ?", userID, group).First(&limitConfig).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果用户限速配置不存在，创建默认配置
			//return s.createDefaultUserLimitConfig(userID, group)
		}
		return nil, err
	}
	return &limitConfig, nil
}

// GetGroupLimitConfig 获取组限速配置
func (s *LimiterStorage) GetGroupLimitConfig(group string) (*models.GWGroupLimitConfig, error) {
	var limitConfig models.GWGroupLimitConfig
	err := s.db.Where("group = ?", group).First(&limitConfig).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果组限速配置不存在，创建默认配置

			//return s.createDefaultGroupLimitConfig(group)
		}
		return nil, err
	}
	return &limitConfig, nil
}

// createDefaultUserLimitConfig 创建默认用户限速配置
func (s *LimiterStorage) createDefaultUserLimitConfig(ctx context.Context, userID, group string) (*models.GWUserLimitConfig, error) {
	defaultConfig := &models.GWUserLimitConfig{
		Group:      group,
		UserId:     userID,
		TotalLimit: 1000.0, // 默认每秒1000配额
		QueryLimit: 100.0,  // 默认单次查询100配额
	}

	err := s.db.Create(ctx, defaultConfig).Error
	if err != nil {
		return nil, fmt.Errorf("创建默认用户限速配置失败: %w", err)
	}

	return defaultConfig, nil
}

// createDefaultGroupLimitConfig 创建默认组限速配置
func (s *LimiterStorage) createDefaultGroupLimitConfig(ctx context.Context, group string) (*models.GWGroupLimitConfig, error) {
	defaultConfig := &models.GWGroupLimitConfig{
		Group:      group,
		TotalLimit: 5000.0, // 默认每秒5000配额
		QueryLimit: 500.0,  // 默认单次查询500配额
	}

	err := s.db.Create(ctx, defaultConfig).Error
	if err != nil {
		return nil, fmt.Errorf("创建默认组限速配置失败: %w", err)
	}

	return defaultConfig, nil
}

// UpdateUserLimitConfig 更新用户限速配置
func (s *LimiterStorage) UpdateUserLimitConfig(userID, group string, totalLimit, queryLimit float64) error {
	err := s.db.Model(&models.GWUserLimitConfig{}).
		Where("user_id = ? AND group = ?", userID, group).
		Updates(map[string]interface{}{
			"total_limit": totalLimit,
			"query_limit": queryLimit,
			"updated_at":  time.Now(),
		}).Error
	if err != nil {
		return fmt.Errorf("更新用户限速配置失败: %w", err)
	}
	return nil
}

// UpdateGroupLimitConfig 更新组限速配置
func (s *LimiterStorage) UpdateGroupLimitConfig(group string, totalLimit, queryLimit float64) error {
	err := s.db.Model(&models.GWGroupLimitConfig{}).
		Where("group = ?", group).
		Updates(map[string]interface{}{
			"total_limit": totalLimit,
			"query_limit": queryLimit,
			"updated_at":  time.Now(),
		}).Error
	if err != nil {
		return fmt.Errorf("更新组限速配置失败: %w", err)
	}
	return nil
}

// GetUserLimitConfigList 获取用户限速配置列表
func (s *LimiterStorage) GetUserLimitConfigList(group string, limit, offset int) ([]models.GWUserLimitConfig, error) {
	var configs []models.GWUserLimitConfig
	query := s.db.Where("group = ?", group)
	if limit > 0 {
		query = query.Limit(limit).Offset(offset)
	}
	err := query.Find(&configs).Error
	return configs, err
}

// GetGroupLimitConfigList 获取组限速配置列表
func (s *LimiterStorage) GetGroupLimitConfigList(limit, offset int) ([]models.GWGroupLimitConfig, error) {
	var configs []models.GWGroupLimitConfig
	query := s.db.DB
	if limit > 0 {
		query = query.Limit(limit).Offset(offset)
	}
	err := query.Find(&configs).Error
	return configs, err
}

// DeleteUserLimitConfig 删除用户限速配置
func (s *LimiterStorage) DeleteUserLimitConfig(userID, group string) error {
	err := s.db.Where("user_id = ? AND group = ?", userID, group).Delete(&models.GWUserLimitConfig{}).Error
	if err != nil {
		return fmt.Errorf("删除用户限速配置失败: %w", err)
	}
	return nil
}

// DeleteGroupLimitConfig 删除组限速配置
func (s *LimiterStorage) DeleteGroupLimitConfig(group string) error {
	err := s.db.Where("group = ?", group).Delete(&models.GWGroupLimitConfig{}).Error
	if err != nil {
		return fmt.Errorf("删除组限速配置失败: %w", err)
	}
	return nil
}
