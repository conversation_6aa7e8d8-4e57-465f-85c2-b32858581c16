package storage

import (
	"context"
	"fmt"
	"time"

	"goalfy_aigateway/internal/models"
	"gorm.io/gorm"
)

// QuotaStorage 配额存储层
type QuotaStorage struct {
	db *CommonDao
}

// NewQuotaStorage 创建配额存储层
func NewQuotaStorage(db *CommonDao) *QuotaStorage {
	return &QuotaStorage{db: db}
}

// GetModelConfig 获取模型配置
func (s *QuotaStorage) GetModelConfig(provider, modelName string) (*models.GWModelConfig, error) {
	var modelConfig models.GWModelConfig
	err := s.db.Where("provider = ? AND model_name = ? AND enabled = ?",
		provider, modelName, true).First(&modelConfig).Error
	if err != nil {
		return nil, fmt.Errorf("模型配置未找到: %s/%s", provider, modelName)
	}
	return &modelConfig, nil
}

// GetUserQuota 获取用户配额配置
func (s *QuotaStorage) GetUserQuota(userID, group string) (*models.GWUserQuotaConfig, error) {
	var userQuota models.GWUserQuotaConfig
	err := s.db.Where("user_id = ? AND group = ?", userID, group).First(&userQuota).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果用户配额不存在，创建默认配额
			//return s.createDefaultUserQuota(userID, group)
		}
		return nil, err
	}
	return &userQuota, nil
}

// GetGroupQuota 获取组配额配置
func (s *QuotaStorage) GetGroupQuota(group string) (*models.GWGroupQuotaConfig, error) {
	var groupQuota models.GWGroupQuotaConfig
	err := s.db.Where("group = ?", group).First(&groupQuota).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
		}
		return nil, err
	}
	return &groupQuota, nil
}

// createDefaultUserQuota 创建默认用户配额
func (s *QuotaStorage) createDefaultUserQuota(ctx context.Context, userID, group string) (*models.GWUserQuotaConfig, error) {
	defaultQuota := &models.GWUserQuotaConfig{
		Group:     group,
		UserId:    userID,
		Quota:     100000.0, // 默认配额
		Used:      0.0,
		TotalUsed: 0.0,
	}

	err := s.db.Create(ctx, defaultQuota).Error
	if err != nil {
		return nil, fmt.Errorf("创建默认用户配额失败: %w", err)
	}

	return defaultQuota, nil
}

// createDefaultGroupQuota 创建默认组配额
func (s *QuotaStorage) createDefaultGroupQuota(ctx context.Context, group string) (*models.GWGroupQuotaConfig, error) {
	defaultQuota := &models.GWGroupQuotaConfig{
		Group:     group,
		Quota:     500000.0, // 默认组配额
		Used:      0.0,
		TotalUsed: 0.0,
	}

	err := s.db.Create(ctx, defaultQuota).Error
	if err != nil {
		return nil, fmt.Errorf("创建默认组配额失败: %w", err)
	}

	return defaultQuota, nil
}

// ConsumeQuota 消费配额
func (s *QuotaStorage) ConsumeQuota(userID, group string, quotaCost float64) error {
	// 在事务中更新配额
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 更新用户配额
		err := tx.Model(&models.GWUserQuotaConfig{}).
			Where("user_id = ? AND group = ?", userID, group).
			Updates(map[string]interface{}{
				"used":       gorm.Expr("used + ?", quotaCost),
				"total_used": gorm.Expr("total_used + ?", quotaCost),
				"updated_at": time.Now(),
			}).Error
		if err != nil {
			return fmt.Errorf("更新用户配额失败: %w", err)
		}

		// 更新组配额
		err = tx.Model(&models.GWGroupQuotaConfig{}).
			Where("group = ?", group).
			Updates(map[string]interface{}{
				"used":       gorm.Expr("used + ?", quotaCost),
				"total_used": gorm.Expr("total_used + ?", quotaCost),
				"updated_at": time.Now(),
			}).Error
		if err != nil {
			return fmt.Errorf("更新组配额失败: %w", err)
		}

		return nil
	})
}

// ResetQuota 重置配额
func (s *QuotaStorage) ResetQuota(group string) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 重置组配额
		err := tx.Model(&models.GWGroupQuotaConfig{}).
			Where("group = ?", group).
			Update("used", 0).Error
		if err != nil {
			return fmt.Errorf("重置组配额失败: %w", err)
		}

		// 重置用户配额
		err = tx.Model(&models.GWUserQuotaConfig{}).
			Where("group = ?", group).
			Update("used", 0).Error
		if err != nil {
			return fmt.Errorf("重置用户配额失败: %w", err)
		}

		return nil
	})
}

// UpdateUserQuota 更新用户配额配置
func (s *QuotaStorage) UpdateUserQuota(userID, group string, quota float64) error {
	err := s.db.Model(&models.GWUserQuotaConfig{}).
		Where("user_id = ? AND group = ?", userID, group).
		Updates(map[string]interface{}{
			"quota":      quota,
			"updated_at": time.Now(),
		}).Error
	if err != nil {
		return fmt.Errorf("更新用户配额配置失败: %w", err)
	}
	return nil
}

// UpdateGroupQuota 更新组配额配置
func (s *QuotaStorage) UpdateGroupQuota(group string, quota float64) error {
	err := s.db.Model(&models.GWGroupQuotaConfig{}).
		Where("group = ?", group).
		Updates(map[string]interface{}{
			"quota":      quota,
			"updated_at": time.Now(),
		}).Error
	if err != nil {
		return fmt.Errorf("更新组配额配置失败: %w", err)
	}
	return nil
}

// GetUserQuotaList 获取用户配额列表
func (s *QuotaStorage) GetUserQuotaList(group string, limit, offset int) ([]models.GWUserQuotaConfig, error) {
	var quotas []models.GWUserQuotaConfig
	query := s.db.Where("group = ?", group)
	if limit > 0 {
		query = query.Limit(limit).Offset(offset)
	}
	err := query.Find(&quotas).Error
	return quotas, err
}

// GetGroupQuotaList 获取组配额列表
func (s *QuotaStorage) GetGroupQuotaList(limit, offset int) ([]models.GWGroupQuotaConfig, error) {
	var quotas []models.GWGroupQuotaConfig
	query := s.db.DB
	if limit > 0 {
		query = query.Limit(limit).Offset(offset)
	}
	err := query.Find(&quotas).Error
	return quotas, err
}
