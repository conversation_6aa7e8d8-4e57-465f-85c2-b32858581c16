#!/bin/bash

# 配额和限速测试脚本

BASE_URL="http://localhost:8080"
USER_ID="test_user_123"
GROUP="premium"

echo "=== 配额和限速功能测试 ==="
echo "Base URL: $BASE_URL"
echo "User ID: $USER_ID"
echo "Group: $GROUP"
echo

# 函数：发送聊天请求
send_chat_request() {
    local model=$1
    local message=$2
    local max_tokens=${3:-100}
    
    echo "发送聊天请求 - Model: $model, Message: $message"
    
    curl -s -X POST "$BASE_URL/v1/chat/completions" \
        -H "Content-Type: application/json" \
        -H "X-User-ID: $USER_ID" \
        -H "X-Group: $GROUP" \
        -d "{
            \"model\": \"$model\",
            \"messages\": [
                {\"role\": \"user\", \"content\": \"$message\"}
            ],
            \"max_tokens\": $max_tokens
        }" | jq '.'
    echo
}

# 函数：查询配额状态
check_quota_status() {
    echo "查询配额状态..."
    curl -s -X GET "$BASE_URL/v1/quota/status" \
        -H "X-User-ID: $USER_ID" \
        -H "X-Group: $GROUP" | jq '.'
    echo
}

# 函数：重置配额
reset_quota() {
    local group=$1
    echo "重置组配额: $group"
    curl -s -X POST "$BASE_URL/v1/admin/quota/reset/$group" | jq '.'
    echo
}

# 测试1: 检查初始配额状态
echo "=== 测试1: 检查初始配额状态 ==="
check_quota_status

# 测试2: 发送正常请求
echo "=== 测试2: 发送正常请求 ==="
send_chat_request "gpt-4" "Hello, how are you?" 50
check_quota_status

# 测试3: 发送多个请求测试配额消耗
echo "=== 测试3: 发送多个请求测试配额消耗 ==="
for i in {1..3}; do
    echo "请求 $i:"
    send_chat_request "gpt-4" "This is test message $i" 100
done
check_quota_status

# 测试4: 发送大量请求测试限速
echo "=== 测试4: 发送大量请求测试限速 ==="
for i in {1..5}; do
    echo "快速请求 $i:"
    send_chat_request "gpt-4" "Quick test $i" 200
    sleep 0.1
done

# 测试5: 测试不同模型
echo "=== 测试5: 测试不同模型 ==="
send_chat_request "claude-sonnet-4-20250514" "Test Claude model" 150
send_chat_request "gpt-3.5-turbo" "Test GPT-3.5 model" 80

# 测试6: 检查最终配额状态
echo "=== 测试6: 检查最终配额状态 ==="
check_quota_status

# 测试7: 重置配额
echo "=== 测试7: 重置配额 ==="
reset_quota "$GROUP"
check_quota_status

echo "=== 测试完成 ==="
