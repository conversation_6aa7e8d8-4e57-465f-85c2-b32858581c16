# Goalfy AI Gateway

## 项目简介
Goalfy AI Gateway 是一个用于统一管理和调用多种大模型（如OpenAI、Azure OpenAI、自建LLM等）的高可用AI网关。它支持模型配置管理、统一接口、可观测性、灰度测试、限流熔断、用户配额等核心功能。

## 主要功能
1. 统一管理模型配置（如API Key等）
2. 统一的模型调用接口，屏蔽不同模型差异
3. 统计 metrics、trace、log（OpenTelemetry）
4. 详细的LLM输入输出内容日志（分析、学习使用）
5. 支持模型灰度测试（请求按比例路由到不同模型）
6. 模型限流、异常熔断机制
7. 支持用户配额（如每小时token消耗）

## 目录结构

```
goalfy_aigateway/
├── cmd/                # 启动入口
│   └── gateway/
├── internal/
│   ├── config/         # 配置中心
│   ├── api/            # 统一接口层
│   ├── adapter/        # 模型适配器
│   ├── metrics/        # metrics/trace/log
│   ├── logger/         # 日志
│   ├── router/         # 灰度路由
│   ├── limiter/        # 限流/熔断
│   ├── quota/          # 用户配额
│   └── storage/        # 日志/配额等存储
├── pkg/                # 可复用组件
├── scripts/            # 部署/运维脚本
├── go.mod
└── README.md
```

## 技术选型
- Web框架：gin/echo/fiber
- 配置管理：viper
- 日志：zap/logrus
- 限流/熔断：go-redis + golang.org/x/time/rate 或 gobreaker
- OpenTelemetry：otel-go
- 存储：MySQL/Redis/文件系统
- 灰度路由：自定义中间件
- 用户配额：Redis计数器

## 快速开始
1. 克隆项目
2. 安装依赖
3. 运行服务

## 贡献指南
欢迎提交issue和PR！ 