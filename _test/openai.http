### privoder-match
POST {{host}}/v1/chat/completions
Content-Type: application/json
X-User-ID: 19527
X-Project-ID: abc
X-Trace-ID: 62116441bd3b04e5c7f58485fd146a62
Authorization: Bearer sk-v1#3vpNzXwybcz1WURBeDSPpUflOjgf5lj9yC8TnblDZm

{
  "model" : "openai/gpt-4.1",
  "messages" : [
    {
      "role" : "system",
      "content" : "请输出思考过程，使用tool回答最终结果"
    },
    {
    "role" : "user",
    "content" : "如何将一只大象装进冰箱?"
  } ],
  "tools" : [ {
    "type" : "function",
    "function" : {
      "name" : "answer",
      "description" : "you must call this tool for final answer",
      "parameters" : {
        "type" : "object",
        "properties" : {
          "content" : {
            "type" : "string",
            "description" : "(required) The final answer content"
          }
        },
        "required" : [ "content" ]
      }
    }
  } ]
}


### group-match
POST http://localhost:8080/v1/chat/completions
Content-Type: application/json
X-User-ID: 19527
X-Project-ID: abc


{
  "model" : "openai/gpt-4-turbo",
  "messages" : [ {
    "role" : "user",
    "content" : "请输出思考过程，并使用tool回答最终结果:\n 如何将一只大象装进冰箱?"
  } ],
  "tools" : [ {
    "type" : "function",
    "function" : {
      "name" : "answer",
      "description" : "you must call this tool for final answer",
      "parameters" : {
        "type" : "object",
        "properties" : {
          "content" : {
            "type" : "string",
            "description" : "(required) The final answer content"
          }
        },
        "required" : [ "content" ]
      }
    }
  } ]
}


