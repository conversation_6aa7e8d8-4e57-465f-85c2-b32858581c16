### group-match
POST {{host}}/v1/chat/completions
Content-Type: application/json
X-User-ID: 19527
X-Project-ID: abc
Authorization: Bearer sk-v1#3vpNzXwybcz1WURBeDSPpUflOjgf5lj9yC8TnblDZm

{
  "model" : "claude/claude-sonnet-4-20250514",
  "messages" : [ {
    "role" : "user",
    "content" : "使用tool回答最终结果:\n 如何将一只大象装进冰箱?"
  } ],
  "thinking": {
    "type": "enabled",
    "budget_tokens": 10000
  },
  "tools" : [ {
    "cache_control": {"type": "ephemeral"},
    "type" : "function",
    "function" : {
      "name" : "answer",
      "description" : "you must call this tool for final answer",
      "parameters" : {
        "type" : "object",
        "properties" : {
          "content" : {
            "type" : "string",
            "description" : "(required) The final answer content"
          }
        },
        "required" : [ "content" ]

      }
    }
  } ]
}


### group-match
POST http://localhost:8080/v1/chat/completions
Content-Type: application/json
X-User-ID: 19527
X-Project-ID: abc


{
  "model" : "openai/gpt-4-turbo",
  "messages" : [ {
    "role" : "user",
    "content" : "请输出思考过程，并使用tool回答最终结果:\n 如何将一只大象装进冰箱?"
  } ],
  "tools" : [ {
    "type" : "function",
    "function" : {
      "name" : "answer",
      "description" : "you must call this tool for final answer",
      "parameters" : {
        "type" : "object",
        "properties" : {
          "content" : {
            "type" : "string",
            "description" : "(required) The final answer content"
          }
        },
        "required" : [ "content" ]
      }
    }
  } ]
}


