# AI Gateway Admin Panel

AI Gateway 的极简风格管理后台，基于 Vue 3 + Element Plus 构建。

## 功能特性

### ✅ 已实现功能

1. **用户认证**
   - 基于密钥的登录系统
   - 登录状态持久化
   - 路由守卫保护

2. **供应商管理**
   - 查看供应商列表
   - 添加/编辑/删除供应商
   - 配置 API 类型、基础 URL、API 密钥等
   - 设置优先级、超时时间、重试次数
   - 启用/禁用供应商状态

3. **模型组管理**
   - 查看模型组列表
   - 创建/编辑/删除模型组
   - 配置负载均衡策略（随机/加权轮询）
   - 管理模型组中的模型配置
   - 设置模型权重和状态

### 🎨 设计特色

- **极简风格**: 清爽的界面设计，专注于功能本身
- **响应式布局**: 适配桌面和移动设备
- **现代化 UI**: 基于 Element Plus 组件库
- **直观操作**: 简洁的交互流程

## 技术栈

- **前端框架**: Vue 3 (Composition API)
- **UI 组件库**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **HTTP 客户端**: Axios
- **构建工具**: Vite
- **开发语言**: JavaScript

## 快速开始

### 1. 安装依赖

```bash
cd ui
npm install
```

### 2. 开发环境启动

```bash
npm run dev
```

访问 http://localhost:3000

### 3. 生产构建

```bash
npm run build
```

构建产物将输出到 `dist` 目录。

## 项目结构

```
ui/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 公共组件
│   ├── layouts/           # 布局组件
│   │   └── MainLayout.vue # 主布局
│   ├── router/            # 路由配置
│   │   └── index.js
│   ├── stores/            # 状态管理
│   │   └── auth.js        # 认证状态
│   ├── utils/             # 工具函数
│   │   └── api.js         # API 请求封装
│   ├── views/             # 页面组件
│   │   ├── Login.vue      # 登录页
│   │   ├── Providers.vue  # 供应商管理
│   │   └── ModelGroups.vue # 模型组管理
│   ├── App.vue            # 根组件
│   ├── main.js            # 入口文件
│   └── style.css          # 全局样式
├── index.html             # HTML 模板
├── package.json           # 项目配置
├── vite.config.js         # Vite 配置
└── README.md              # 项目说明
```

## 配置说明

### API 代理配置

开发环境下，前端请求会代理到后端服务：

```javascript
// vite.config.js
export default defineConfig({
  base: '/admin/',
  server: {
    proxy: {
      '/admin': {
        target: 'http://localhost:8080',
        changeOrigin: true
      }
    }
  }
})
```

### 登录密钥配置

登录密钥需要在后端配置文件中设置，前端通过 `/admin/api/login` 接口进行验证。

## API 接口

### 认证接口

- `POST /admin/api/login` - 管理员登录

### 供应商管理接口

- `GET /admin/api/providers` - 获取供应商列表
- `POST /admin/api/providers` - 创建供应商
- `PUT /admin/api/providers/:name` - 更新供应商
- `PATCH /admin/api/providers/:name/status` - 更新供应商状态
- `DELETE /admin/api/providers/:name` - 删除供应商
- `GET /admin/api/providers/:name/models` - 获取供应商模型列表

### 模型组管理接口

- `GET /admin/api/model-groups` - 获取模型组列表
- `POST /admin/api/model-groups` - 创建模型组
- `PUT /admin/api/model-groups/:name` - 更新模型组
- `PATCH /admin/api/model-groups/:name/status` - 更新模型组状态
- `DELETE /admin/api/model-groups/:name` - 删除模型组

## 部署说明

### 1. 构建生产版本

```bash
npm run build
```

### 2. 部署到 Web 服务器

将 `dist` 目录下的文件部署到 Web 服务器（如 Nginx、Apache）。

### 3. Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/ui/dist;
    index index.html;

    # Admin UI 路由支持
    location /admin/ {
        try_files $uri $uri/ /index.html;
    }

    # API 代理到后端
    location /admin/api/ {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 开发指南

### 添加新页面

1. 在 `src/views/` 目录下创建新的 Vue 组件
2. 在 `src/router/index.js` 中添加路由配置
3. 在侧边栏菜单中添加导航链接

### 添加新的 API 接口

1. 在对应的页面组件中使用 `api` 工具发起请求
2. 请求会自动添加认证头和错误处理

### 样式定制

- 全局样式定义在 `src/style.css`
- 组件样式使用 scoped CSS
- 支持 CSS 变量和响应式设计

## 注意事项

1. **安全性**: 管理后台应该部署在内网环境或添加额外的访问控制
2. **权限控制**: 当前版本使用简单的密钥认证，生产环境建议使用更完善的权限系统
3. **数据验证**: 前端验证仅用于用户体验，后端必须进行完整的数据验证
4. **错误处理**: 已实现基础的错误处理，可根据需要扩展

## 后续计划

- [ ] 配额管理界面
- [ ] 限速配置界面
- [ ] 系统监控面板
- [ ] 日志查看功能
- [ ] 用户权限管理
- [ ] 主题切换功能
