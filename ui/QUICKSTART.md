# 快速开始指南

## 🚀 一键启动

### 方式一：使用启动脚本（推荐）

```bash
cd ui
./start.sh
```

### 方式二：手动启动

```bash
cd ui
npm install
npm run dev
```

## 📱 访问管理后台

1. 打开浏览器访问：http://localhost:3000
2. 使用配置文件中的管理员密钥登录
3. 开始管理您的 AI Gateway

## 🔑 登录配置

在后端配置文件中设置管理员密钥：

```yaml
# etc/config.yaml
admin:
  login_key: "your-admin-key-here"
```

## 📋 功能清单

### ✅ 供应商管理
- [x] 查看供应商列表
- [x] 添加新供应商
- [x] 编辑供应商配置
- [x] 启用/禁用供应商
- [x] 删除供应商
- [x] 配置 API 密钥和基础 URL

### ✅ 模型组管理
- [x] 查看模型组列表
- [x] 创建新模型组
- [x] 编辑模型组配置
- [x] 设置负载均衡策略
- [x] 管理模型权重
- [x] 启用/禁用模型组

### ✅ 用户界面
- [x] 极简风格设计
- [x] 响应式布局
- [x] 直观的操作流程
- [x] 实时状态更新

## 🛠️ 开发环境

- **Node.js**: >= 16.0.0
- **npm**: >= 7.0.0
- **浏览器**: Chrome, Firefox, Safari, Edge

## 📦 生产部署

### 1. 构建生产版本

```bash
npm run build
```

### 2. 部署到服务器

将 `dist` 目录部署到 Web 服务器，配置反向代理到后端 API。

### 3. Nginx 配置示例

```nginx
server {
    listen 80;
    server_name admin.your-domain.com;
    root /path/to/ui/dist;
    index index.html;

    # Admin UI 路由支持
    location /admin/ {
        try_files $uri $uri/ /index.html;
    }

    # 重定向根路径到admin
    location = / {
        return 301 /admin/;
    }

    # API 代理到后端
    location /admin/api/ {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🔧 常见问题

### Q: 登录失败怎么办？
A: 检查后端配置文件中的 `admin.login_key` 设置是否正确。

### Q: 页面显示网络错误？
A: 确保后端服务正在运行，并检查 API 代理配置。

### Q: 如何修改默认端口？
A: 修改 `vite.config.js` 中的 `server.port` 配置。

### Q: 如何自定义主题？
A: 修改 `src/style.css` 中的 CSS 变量和样式。

## 📞 技术支持

如果遇到问题，请检查：

1. Node.js 版本是否符合要求
2. 依赖是否正确安装
3. 后端服务是否正常运行
4. 网络连接是否正常

## 🎯 下一步

管理后台已经准备就绪！您可以：

1. 配置您的第一个 AI 供应商
2. 创建模型组进行负载均衡
3. 测试 API 调用和路由功能
4. 监控系统运行状态

祝您使用愉快！ 🎉
