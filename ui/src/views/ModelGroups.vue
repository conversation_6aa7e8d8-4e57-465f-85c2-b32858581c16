<template>
  <div class="model-groups-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">模型组管理</h1>
      <p class="page-description">管理模型组配置，设置负载均衡策略和模型权重分配</p>
    </div>

    <!-- 操作栏 -->
    <div class="page-actions" style="margin-bottom: 24px;">
      <el-button
        type="primary"
        class="minimal-button"
        @click="showAddDialog"
      >
        <el-icon><Plus /></el-icon>
        添加模型组
      </el-button>
      <el-button
        class="minimal-button"
        @click="loadModelGroups"
      >
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <!-- 模型组列表 -->
    <el-card class="minimal-card" style="flex: 1; display: flex; flex-direction: column;">
      <el-table
        v-loading="loading"
        :data="modelGroups"
        class="minimal-table"
        style="width: 100%; height: 100%;"
        :height="'100%'"
      >
        <el-table-column type="expand">
          <template #default="{ row }">
            <div style="padding: 16px;">
              <h4 style="margin-bottom: 12px; color: #374151;">模型配置</h4>
              <el-table
                :data="row.Models || row.models || []"
                size="small"
                style="width: 100%;"
              >
                <el-table-column label="服务账号" width="120">
                  <template #default="{ row: modelRow }">
                    {{ modelRow.Model?.Provider || modelRow.provider || '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="模型名称" width="200">
                  <template #default="{ row: modelRow }">
                    {{ modelRow.Model?.ModelName || modelRow.modelName || modelRow.model || '-' }}
                  </template>
                </el-table-column>
                <el-table-column label="权重" width="100">
                  <template #default="{ row: modelRow }">
                    {{ modelRow.Weight || modelRow.weight || 1.0 }}
                  </template>
                </el-table-column>
                <el-table-column label="状态" width="100">
                  <template #default="{ row: modelRow }">
                    <el-tag
                      :type="(modelRow.Enabled !== undefined ? modelRow.Enabled : modelRow.enabled) ? 'success' : 'danger'"
                      size="small"
                    >
                      {{ (modelRow.Enabled !== undefined ? modelRow.Enabled : modelRow.enabled) ? '启用' : '禁用' }}
                    </el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="组名称" width="150">
          <template #default="{ row }">
            {{ row.Name || row.name }}
          </template>
        </el-table-column>
        <el-table-column label="策略" width="120">
          <template #default="{ row }">
            <el-tag size="small">
              {{ (row.Strategy || row.strategy) === 'weighted' ? '加权轮询' : '随机选择' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="模型数量" width="100">
          <template #default="{ row }">
            {{ (row.Models || row.models || []).length }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="(row.Enabled !== undefined ? row.Enabled : row.enabled) ? 'success' : 'danger'"
              size="small"
            >
              {{ (row.Enabled !== undefined ? row.Enabled : row.enabled) ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.CreatedAt || row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              text
              @click="showEditDialog(row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              text
              @click="deleteModelGroup(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑模型组' : '添加模型组'"
      width="800px"
      class="minimal-dialog"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="组名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入模型组名称"
            :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="负载策略" prop="strategy">
          <el-radio-group v-model="form.strategy">
            <el-radio label="random">随机选择</el-radio>
            <el-radio label="weighted">加权轮询</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="状态" prop="enabled">
          <el-radio-group v-model="form.enabled">
            <el-radio :label="true">启用</el-radio>
            <el-radio :label="false">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <!-- 模型配置 -->
        <el-form-item label="模型配置">
          <div style="width: 100%;">
            <div style="margin-bottom: 12px;">
              <el-button
                type="primary"
                size="small"
                @click="addModel"
              >
                <el-icon><Plus /></el-icon>
                添加模型
              </el-button>
            </div>
            
            <el-table
              :data="form.models"
              size="small"
              style="width: 100%;"
            >
              <el-table-column label="模型" width="300">
                <template #default="{ row, $index }">
                  <el-select
                    v-model="row.modelId"
                    placeholder="选择模型"
                    size="small"
                    filterable
                    @change="onModelChange($index)"
                  >
                    <el-option
                      v-for="model in availableModels"
                      :key="model.id"
                      :label="model.displayName"
                      :value="model.id"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="权重" width="100">
                <template #default="{ row }">
                  <el-input-number
                    v-model="row.weight"
                    :min="0.1"
                    :max="10"
                    :step="0.1"
                    size="small"
                    :disabled="form.strategy === 'random'"
                  />
                </template>
              </el-table-column>
              <el-table-column label="状态" width="100">
                <template #default="{ row }">
                  <el-switch
                    v-model="row.enabled"
                    size="small"
                  />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80">
                <template #default="{ $index }">
                  <el-button
                    type="danger"
                    size="small"
                    text
                    @click="removeModel($index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="submitForm"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import api from '@/utils/api'

// 响应式数据
const loading = ref(false)
const modelGroups = ref([])
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const formRef = ref()

// 表单数据
const form = reactive({
  id: null,
  name: '',
  strategy: 'weighted',
  enabled: true,
  createdAt: null,
  updatedAt: null,
  models: []
})

// 可用模型列表（用于选择）
const availableModels = ref([])

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入模型组名称', trigger: 'blur' }
  ],
  strategy: [
    { required: true, message: '请选择负载策略', trigger: 'change' }
  ]
}

// 加载模型组列表
const loadModelGroups = async () => {
  loading.value = true
  try {
    const response = await api.get('/model-groups')
    modelGroups.value = response.data.data || []
  } catch (error) {
    ElMessage.error('加载模型组列表失败')
  } finally {
    loading.value = false
  }
}

// 加载所有可用模型
const loadAvailableModels = async () => {
  try {
    const response = await api.get('/providers')
    const providers = response.data.data || []

    const allModels = []
    // 加载每个服务账号的模型列表
    for (const provider of providers) {
      try {
        const modelResponse = await api.get(`/providers/models/${provider.Name}`)
        const models = modelResponse.data.data || []
        models.forEach(model => {
          allModels.push({
            id: model.ID,
            provider: model.Provider,
            modelName: model.ModelName,
            displayName: `${model.Provider}/${model.ModelName}`,
            enabled: model.Enabled
          })
        })
      } catch (error) {
        console.error(`加载服务账号 ${provider.Name} 的模型失败:`, error)
      }
    }

    availableModels.value = allModels.filter(model => model.enabled)
  } catch (error) {
    ElMessage.error('加载可用模型列表失败')
  }
}

// 模型变化时的处理
const onModelChange = (index) => {
  const modelId = form.models[index].modelId
  const selectedModel = availableModels.value.find(model => model.id === modelId)
  if (selectedModel) {
    form.models[index].provider = selectedModel.provider
    form.models[index].modelName = selectedModel.modelName
  }
}

// 显示添加对话框
const showAddDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (row) => {
  isEdit.value = true
  Object.assign(form, {
    id: row.ID || row.id,
    name: row.Name || row.name,
    strategy: row.Strategy || row.strategy,
    enabled: row.Enabled !== undefined ? row.Enabled : row.enabled,
    createdAt: row.CreatedAt || row.created_at,
    updatedAt: row.UpdatedAt || row.updated_at,
    models: (row.Models || row.models || []).map(model => ({
      id: model.ID || model.id,
      groupId: model.GroupID || model.group_id || (row.ID || row.id),
      modelId: model.ModelID || model.Model?.ID,
      provider: model.Model?.Provider || model.provider,
      modelName: model.Model?.ModelName || model.modelName || model.model,
      weight: model.Weight || model.weight || 1.0,
      enabled: model.Enabled !== undefined ? model.Enabled : model.enabled,
      createdAt: model.CreatedAt || model.created_at,
      updatedAt: model.UpdatedAt || model.updated_at
    }))
  })
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    id: null,
    name: '',
    strategy: 'weighted',
    enabled: true,
    createdAt: null,
    updatedAt: null,
    models: []
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 添加模型
const addModel = () => {
  form.models.push({
    id: null,
    groupId: form.id,
    modelId: null,
    provider: '',
    modelName: '',
    weight: 1.0,
    enabled: true,
    createdAt: null,
    updatedAt: null
  })
}

// 删除模型
const removeModel = (index) => {
  form.models.splice(index, 1)
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return

  try {
    const valid = await formRef.value.validate()
    if (!valid) return

    if (form.models.length === 0) {
      ElMessage.error('请至少添加一个模型')
      return
    }

    // 验证所有模型都已选择
    for (let i = 0; i < form.models.length; i++) {
      if (!form.models[i].modelId) {
        ElMessage.error(`请选择第 ${i + 1} 个模型`)
        return
      }
    }

    submitting.value = true

    if (isEdit.value) {
      // 更新模式：发送完整的对象，包括所有字段
      const submitData = {
        // GWModelGroup 字段
        ID: form.id,
        Name: form.name,
        Strategy: form.strategy,
        Enabled: form.enabled,
        CreatedAt: form.createdAt,
        UpdatedAt: form.updatedAt,
        // Models 数组
        Models: form.models.map(model => ({
          ID: model.id,
          GroupID: model.groupId || form.id,
          ModelID: model.modelId,
          Weight: model.weight,
          Enabled: model.enabled,
          CreatedAt: model.createdAt,
          UpdatedAt: model.updatedAt
        }))
      }

      await api.put(`/model-groups/${form.name}`, submitData)
      ElMessage.success('更新成功')
    } else {
      // 创建模式：只发送必要字段
      const submitData = {
        name: form.name,
        strategy: form.strategy,
        enabled: form.enabled,
        models: form.models.map(model => ({
          modelId: model.modelId,
          weight: model.weight,
          enabled: model.enabled
        }))
      }

      await api.post('/model-groups', submitData)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    loadModelGroups()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    submitting.value = false
  }
}

// 切换状态
const toggleStatus = async (row) => {
  try {
    const currentStatus = row.Enabled !== undefined ? row.Enabled : row.enabled
    const newStatus = !currentStatus
    const groupName = row.Name || row.name
    await api.patch(`/model-groups/${groupName}/status`, { enabled: newStatus })

    // 更新本地数据
    if (row.Enabled !== undefined) {
      row.Enabled = newStatus
    } else {
      row.enabled = newStatus
    }

    ElMessage.success('状态更新成功')
  } catch (error) {
    ElMessage.error('状态更新失败')
  }
}

// 删除模型组
const deleteModelGroup = async (row) => {
  try {
    const groupName = row.Name || row.name
    await ElMessageBox.confirm(
      `确定要删除模型组 "${groupName}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await api.delete(`/model-groups/${groupName}`)
    ElMessage.success('删除成功')
    loadModelGroups()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 组件挂载时加载数据
onMounted(() => {
  loadAvailableModels()
  loadModelGroups()
})
</script>

<style scoped>
.model-groups-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-actions {
  display: flex;
  gap: 12px;
}

:deep(.minimal-dialog .el-dialog__header) {
  padding: 24px 24px 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.minimal-dialog .el-dialog__body) {
  padding: 24px;
}

:deep(.minimal-dialog .el-dialog__footer) {
  padding: 0 24px 24px;
  border-top: 1px solid #f0f0f0;
}

:deep(.el-table__expanded-cell) {
  background-color: #fafbfc;
}
</style>
