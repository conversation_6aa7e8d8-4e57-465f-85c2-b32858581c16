<template>
  <div class="providers-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1 class="page-title">服务账号管理</h1>
      <p class="page-description">管理AI模型服务账号配置，包括API密钥、基础URL等设置</p>
    </div>

    <!-- 操作栏 -->
    <div class="page-actions" style="margin-bottom: 24px;">
      <el-button
        type="primary"
        class="minimal-button"
        @click="showAddDialog"
      >
        <el-icon><Plus /></el-icon>
        添加服务账号
      </el-button>
      <el-button
        class="minimal-button"
        @click="loadProviders"
      >
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <!-- 服务账号列表 -->
    <el-card class="minimal-card" style="flex: 1; display: flex; flex-direction: column;">
      <el-table
        v-loading="loading"
        :data="providers"
        class="minimal-table"
        style="width: 100%; height: 100%;"
        :height="'100%'"
        :expand-row-keys="expandedRows"
        row-key="Name"
        @expand-change="handleExpandChange"
      >
        <el-table-column type="expand">
          <template #default="{ row }">
            <div class="expanded-content">
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                <h4 style="margin: 0; color: #409eff;">{{ row.Name }} - 模型列表</h4>
                <el-button
                  type="primary"
                  size="small"
                  @click="addModel(row)"
                >
                  <el-icon><Plus /></el-icon>
                  添加模型
                </el-button>
              </div>
              <div class="models-table-container">
                <el-table
                  v-loading="row.modelsLoading"
                  :data="row.models || []"
                  size="small"
                  style="width: 100%;"
                >
                <el-table-column prop="ModelName" label="模型名称" width="180" />
                <el-table-column prop="TokensInputPerQuota" label="输入配额(Token)" width="140">
                  <template #default="{ row: model }">
                    {{ model.TokensInputPerQuota?.toLocaleString() || '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="TokensOutputPerQuota" label="输出配额(Token)" width="140">
                  <template #default="{ row: model }">
                    {{ model.TokensOutputPerQuota?.toLocaleString() || '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="MaxTokens" label="最大Token" width="100">
                  <template #default="{ row: model }">
                    {{ model.MaxTokens?.toLocaleString() || '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="Temperature" label="温度" width="80">
                  <template #default="{ row: model }">
                    {{ model.Temperature?.toFixed(1) || '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="TopP" label="Top P" width="80">
                  <template #default="{ row: model }">
                    {{ model.TopP?.toFixed(1) || '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="CreatedAt" label="创建时间" width="160">
                  <template #default="{ row: model }">
                    {{ formatDate(model.CreatedAt) }}
                  </template>
                </el-table-column>
                <el-table-column prop="UpdatedAt" label="更新时间" width="160">
                  <template #default="{ row: model }">
                    {{ formatDate(model.UpdatedAt) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="180" fixed="right">
                  <template #default="{ row: model }">
                    <el-button
                      type="primary"
                      size="small"
                      text
                      @click="editModel(row, model)"
                    >
                      编辑
                    </el-button>
                    <el-button
                      type="warning"
                      size="small"
                      text
                      @click="toggleModelStatus(row, model)"
                    >
                      {{ model.Enabled ? '禁用' : '启用' }}
                    </el-button>
                    <el-button
                      type="danger"
                      size="small"
                      text
                      @click="deleteModel(row, model)"
                    >
                      删除
                    </el-button>
                  </template>
                </el-table-column>
                </el-table>
                <div v-if="!row.models || row.models.length === 0" style="text-align: center; color: #999; padding: 20px;">
                  暂无模型配置
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="Name" label="服务账号名称" width="150" />
        <el-table-column prop="ApiType" label="API类型" width="120" />
        <el-table-column prop="BaseUrl" label="基础URL" min-width="200" />
        <el-table-column prop="Priority" label="优先级" width="100" />
        <el-table-column prop="Status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="row.Status === 'active' ? 'success' : 'danger'"
              size="small"
            >
              {{ row.Status === 'active' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="CreatedAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.CreatedAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <el-button
              type="info"
              size="small"
              text
              @click="toggleExpand(row)"
            >
              <el-icon><Grid /></el-icon>
              {{ isExpanded(row) ? '收起模型' : '模型配置' }}
            </el-button>
            <el-button
              type="primary"
              size="small"
              text
              @click="showEditDialog(row)"
            >
              编辑
            </el-button>
            
            <el-button
              type="danger"
              size="small"
              text
              @click="deleteProvider(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑服务账号' : '添加服务账号'"
      width="600px"
      class="minimal-dialog"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="服务账号名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入服务账号名称"
            :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="API类型" prop="api_type">
          <el-select v-model="form.api_type" placeholder="请选择API类型">
            <el-option label="OpenAI" value="openai" />
            <el-option label="Azure OpenAI" value="azure" />
            <el-option label="Claude" value="claude" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>
        <el-form-item label="基础URL" prop="base_url">
          <el-input
            v-model="form.base_url"
            placeholder="请输入API基础URL"
          />
        </el-form-item>
        <el-form-item label="API密钥" prop="api_key">
          <el-input
            v-model="form.api_key"
            type="password"
            :placeholder="isEdit ? '留空则不修改密钥' : '请输入API密钥'"
            show-password
          />
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-input-number
            v-model="form.priority"
            :min="1"
            :max="1000"
            placeholder="数值越小优先级越高"
          />
        </el-form-item>
        <el-form-item label="超时时间" prop="timeout">
          <el-input-number
            v-model="form.timeout"
            :min="1000"
            :max="300000"
            placeholder="毫秒"
          />
        </el-form-item>
        <el-form-item label="重试次数" prop="max_retries">
          <el-input-number
            v-model="form.max_retries"
            :min="0"
            :max="10"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="active">启用</el-radio>
            <el-radio label="inactive">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="submitForm"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 模型编辑对话框 -->
    <el-dialog
      v-model="modelDialogVisible"
      :title="isModelEdit ? '编辑模型' : '添加模型'"
      width="600px"
      class="minimal-dialog"
    >
      <el-form
        ref="modelFormRef"
        :model="modelForm"
        :rules="modelRules"
        label-width="120px"
      >
        <el-form-item label="模型名称" prop="model_name">
          <el-input
            v-model="modelForm.model_name"
            placeholder="请输入模型名称，如：gpt-3.5-turbo"
            :disabled="isModelEdit"
          />
        </el-form-item>
        <el-form-item label="输入配额(Token)" prop="tokens_input_per_quota">
          <el-input
            v-model.number="modelForm.tokens_input_per_quota"
            type="number"
            placeholder="1个配额可使用的输入token数量，如：1000000"
          />
          <div style="font-size: 12px; color: #999; margin-top: 4px;">
            表示1个配额可以使用多少个输入token，如：1000000表示1配额=100万输入token
          </div>
        </el-form-item>
        <el-form-item label="输出配额(Token)" prop="tokens_output_per_quota">
          <el-input
            v-model.number="modelForm.tokens_output_per_quota"
            type="number"
            placeholder="1个配额可使用的输出token数量，如：1000000"
          />
          <div style="font-size: 12px; color: #999; margin-top: 4px;">
            表示1个配额可以使用多少个输出token，如：1000000表示1配额=100万输出token
          </div>
        </el-form-item>
        <el-form-item label="最大Token" prop="max_tokens">
          <el-input
            v-model.number="modelForm.max_tokens"
            type="number"
            placeholder="模型支持的最大token数，如：4096"
          />
        </el-form-item>
        <el-form-item label="温度" prop="temperature">
          <el-input-number
            v-model="modelForm.temperature"
            :min="0"
            :max="2"
            :step="0.1"
            :precision="1"
            placeholder="控制输出随机性"
          />
        </el-form-item>
        <el-form-item label="Top P" prop="top_p">
          <el-input-number
            v-model="modelForm.top_p"
            :min="0"
            :max="1"
            :step="0.1"
            :precision="1"
            placeholder="核采样参数"
          />
        </el-form-item>
        <el-form-item label="状态" prop="enabled">
          <el-radio-group v-model="modelForm.enabled">
            <el-radio :label="true">启用</el-radio>
            <el-radio :label="false">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="modelDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="modelSubmitting"
          @click="submitModelForm"
        >
          {{ isModelEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import api from '@/utils/api'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const providers = ref([])
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const formRef = ref()
const expandedRows = ref([]) // 展开的行

// 模型编辑相关
const modelDialogVisible = ref(false)
const modelSubmitting = ref(false)
const modelFormRef = ref()
const currentProvider = ref(null)
const currentModel = ref(null)
const isModelEdit = ref(false)

// 表单数据
const form = reactive({
  name: '',
  api_type: '',
  base_url: '',
  api_key: '',
  priority: 100,
  timeout: 30000,
  max_retries: 3,
  status: 'active'
})

// 表单验证规则
const getRules = () => ({
  name: [
    { required: true, message: '请输入服务账号名称', trigger: 'blur' }
  ],
  api_type: [
    { required: true, message: '请选择API类型', trigger: 'change' }
  ],
  base_url: [
    { required: true, message: '请输入基础URL', trigger: 'blur' },
    { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
  ],
  api_key: [
    { required: !isEdit.value, message: '请输入API密钥', trigger: 'blur' }
  ]
})

const rules = ref(getRules())

// 模型表单数据
const modelForm = reactive({
  model_name: '',
  tokens_input_per_quota: 1000000,
  tokens_output_per_quota: 1000000,
  max_tokens: 4096,
  temperature: 0.7,
  top_p: 1.0,
  enabled: true
})

// 模型表单验证规则
const modelRules = {
  model_name: [
    { required: true, message: '请输入模型名称', trigger: 'blur' }
  ],
  tokens_input_per_quota: [
    { required: true, message: '请输入输入配额数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '配额数量必须大于0', trigger: 'blur' }
  ],
  tokens_output_per_quota: [
    { required: true, message: '请输入输出配额数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '配额数量必须大于0', trigger: 'blur' }
  ]
}

// 加载服务账号列表
const loadProviders = async () => {
  loading.value = true
  try {
    const response = await api.get('/providers')
    providers.value = response.data.data || []
  } catch (error) {
    ElMessage.error('加载服务账号列表失败')
  } finally {
    loading.value = false
  }
}

// 显示添加对话框
const showAddDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (row) => {
  isEdit.value = true
  // 映射后端字段名到前端字段名
  Object.assign(form, {
    name: row.Name,
    api_type: row.ApiType,
    base_url: row.BaseUrl,
    api_key: '', // 编辑时不显示密钥
    priority: row.Priority,
    timeout: row.Timeout,
    max_retries: row.MaxRetries,
    status: row.Status
  })
  // 更新验证规则
  rules.value = getRules()
  dialogVisible.value = true
}



// 检查行是否展开
const isExpanded = (row) => {
  return expandedRows.value.includes(row.Name)
}

// 切换展开状态
const toggleExpand = (row) => {
  const index = expandedRows.value.indexOf(row.Name)
  if (index > -1) {
    // 收起
    expandedRows.value.splice(index, 1)
  } else {
    // 展开
    expandedRows.value.push(row.Name)
    // 如果还没有加载模型数据，则加载
    if (!row.models) {
      loadProviderModels(row)
    }
  }
}

// 处理表格展开变化
const handleExpandChange = (row, expandedRows) => {
  // 这个方法会在用户点击展开箭头时触发
  if (expandedRows.includes(row)) {
    // 展开时加载模型数据
    if (!row.models) {
      loadProviderModels(row)
    }
  }
}

// 加载指定服务账号的模型列表
const loadProviderModels = async (provider) => {
  // 设置加载状态
  provider.modelsLoading = true

  try {
    const response = await api.get(`/providers/models/${provider.Name}`)
    provider.models = response.data.data || []
  } catch (error) {
    ElMessage.error(`加载 ${provider.Name} 的模型列表失败`)
    provider.models = []
  } finally {
    provider.modelsLoading = false
  }
}

// 添加模型
const addModel = (provider) => {
  currentProvider.value = provider
  currentModel.value = null
  isModelEdit.value = false

  // 重置表单数据
  Object.assign(modelForm, {
    model_name: '',
    tokens_input_per_quota: 1000000,
    tokens_output_per_quota: 1000000,
    max_tokens: 4096,
    temperature: 0.7,
    top_p: 1.0,
    enabled: true
  })

  modelDialogVisible.value = true
}

// 编辑模型
const editModel = (provider, model) => {
  currentProvider.value = provider
  currentModel.value = model
  isModelEdit.value = true

  // 填充表单数据
  Object.assign(modelForm, {
    model_name: model.ModelName,
    tokens_input_per_quota: model.TokensInputPerQuota,
    tokens_output_per_quota: model.TokensOutputPerQuota,
    max_tokens: model.MaxTokens,
    temperature: model.Temperature,
    top_p: model.TopP,
    enabled: model.Enabled
  })

  modelDialogVisible.value = true
}

// 提交模型表单
const submitModelForm = async () => {
  if (!modelFormRef.value) return

  try {
    const valid = await modelFormRef.value.validate()
    if (!valid) return

    modelSubmitting.value = true

    const data = {
      ...modelForm,
      provider: currentProvider.value.Name
    }

    if (isModelEdit.value) {
      // 编辑模式
      await api.put(`/providers/models/${currentProvider.value.Name}/${modelForm.model_name}`, data)
      ElMessage.success('更新成功')

      // 更新本地数据
      Object.assign(currentModel.value, {
        TokensInputPerQuota: modelForm.tokens_input_per_quota,
        TokensOutputPerQuota: modelForm.tokens_output_per_quota,
        MaxTokens: modelForm.max_tokens,
        Temperature: modelForm.temperature,
        TopP: modelForm.top_p,
        Enabled: modelForm.enabled
      })
    } else {
      // 添加模式
      await api.post(`/providers/models/${currentProvider.value.Name}`, data)
      ElMessage.success('创建成功')

      // 添加到本地数据
      const newModel = {
        ModelName: modelForm.model_name,
        TokensInputPerQuota: modelForm.tokens_input_per_quota,
        TokensOutputPerQuota: modelForm.tokens_output_per_quota,
        MaxTokens: modelForm.max_tokens,
        Temperature: modelForm.temperature,
        TopP: modelForm.top_p,
        Enabled: modelForm.enabled,
        CreatedAt: new Date().toISOString(),
        UpdatedAt: new Date().toISOString()
      }

      if (!currentProvider.value.models) {
        currentProvider.value.models = []
      }
      currentProvider.value.models.push(newModel)
    }

    modelDialogVisible.value = false
  } catch (error) {
    ElMessage.error(isModelEdit.value ? '更新失败' : '创建失败')
  } finally {
    modelSubmitting.value = false
  }
}

// 切换模型状态
const toggleModelStatus = async (provider, model) => {
  try {
    const newStatus = !model.Enabled
    await api.patch(`/providers/models/${provider.Name}/${model.ModelName}/status`, { enabled: newStatus })
    model.Enabled = newStatus
    ElMessage.success('状态更新成功')
  } catch (error) {
    ElMessage.error('状态更新失败')
  }
}

// 删除模型
const deleteModel = async (provider, model) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模型 "${model.ModelName}" 吗？删除后可以通过数据库恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await api.delete(`/providers/models/${provider.Name}/${model.ModelName}`)
    ElMessage.success('删除成功（软删除，可通过数据库恢复）')

    // 重新加载模型列表以获取最新状态（软删除的记录不会再显示）
    await loadProviderModels(provider)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    name: '',
    api_type: '',
    base_url: '',
    api_key: '',
    priority: 100,
    timeout: 30000,
    max_retries: 3,
    status: 'active'
  })
  // 更新验证规则
  rules.value = getRules()
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    submitting.value = true
    
    if (isEdit.value) {
      await api.put(`/providers/${form.name}`, form)
      ElMessage.success('更新成功')
    } else {
      await api.post('/providers', form)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    loadProviders()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    submitting.value = false
  }
}

// 切换状态
const toggleStatus = async (row) => {
  try {
    const newStatus = row.Status === 'active' ? 'inactive' : 'active'
    await api.patch(`/providers/${row.Name}/status`, { status: newStatus })
    row.Status = newStatus
    ElMessage.success('状态更新成功')
  } catch (error) {
    ElMessage.error('状态更新失败')
  }
}

// 删除服务账号
const deleteProvider = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除服务账号 "${row.Name}" 吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await api.delete(`/providers/${row.Name}`)
    ElMessage.success('删除成功')
    loadProviders()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 组件挂载时加载数据
onMounted(() => {
  loadProviders()
})
</script>

<style scoped>
.providers-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-actions {
  display: flex;
  gap: 12px;
}

:deep(.minimal-dialog .el-dialog__header) {
  padding: 24px 24px 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.minimal-dialog .el-dialog__body) {
  padding: 24px;
}

:deep(.minimal-dialog .el-dialog__footer) {
  padding: 0 24px 24px;
  border-top: 1px solid #f0f0f0;
}

.expanded-content {
  padding: 20px 24px;
  background-color: #fafbfc;
  border-radius: 8px;
  margin: 12px 0;
  border-left: 4px solid #409eff;
  width: 100%;
}

.models-table-container {
  width: 95%;
  margin: 0 auto;
}

.expanded-content h4 {
  margin: 0 0 16px 0;
  color: #409eff;
  font-weight: 600;
  font-size: 16px;
}

.expanded-content .el-table {
  background-color: white;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.expanded-content .el-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #374151;
}

.expanded-content .el-table td {
  border-bottom: 1px solid #f3f4f6;
}

.expanded-content .el-table .el-table__row:hover {
  background-color: #f8fafc;
}
</style>
