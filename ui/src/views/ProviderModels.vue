<template>
  <div class="provider-models-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div style="display: flex; align-items: center; gap: 16px;">
        <el-button
          type="info"
          text
          @click="goBack"
        >
          <el-icon><ArrowLeft /></el-icon>
          返回服务账号列表
        </el-button>
        <div>
          <h1 class="page-title">{{ providerName }} - 模型配置</h1>
          <p class="page-description">管理该服务账号下的模型配置，包括配额设置、启用状态等</p>
        </div>
      </div>
    </div>

    <!-- 操作栏 -->
    <div class="page-actions" style="margin-bottom: 24px;">
      <el-button
        type="primary"
        class="minimal-button"
        @click="showAddDialog"
      >
        <el-icon><Plus /></el-icon>
        添加模型
      </el-button>
      <el-button
        class="minimal-button"
        @click="loadModels"
      >
        <el-icon><Refresh /></el-icon>
        刷新
      </el-button>
    </div>

    <!-- 模型列表 -->
    <el-card class="minimal-card" style="flex: 1; display: flex; flex-direction: column;">
      <el-table
        v-loading="loading"
        :data="models"
        class="minimal-table"
        style="width: 100%; height: 100%;"
        :height="'100%'"
      >
        <el-table-column prop="ModelName" label="模型名称" width="200" />
        <el-table-column prop="TokensInputPerQuota" label="输入配额(Token)" width="150">
          <template #default="{ row }">
            {{ row.TokensInputPerQuota.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="TokensOutputPerQuota" label="输出配额(Token)" width="150">
          <template #default="{ row }">
            {{ row.TokensOutputPerQuota.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="MaxTokens" label="最大Token" width="100" />
        <el-table-column prop="Temperature" label="温度" width="80">
          <template #default="{ row }">
            {{ row.Temperature.toFixed(1) }}
          </template>
        </el-table-column>
        <el-table-column prop="Enabled" label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="row.Enabled ? 'success' : 'danger'"
              size="small"
            >
              {{ row.Enabled ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="CreatedAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.CreatedAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              text
              @click="showEditDialog(row)"
            >
              编辑
            </el-button>
            <el-button
              type="warning"
              size="small"
              text
              @click="toggleStatus(row)"
            >
              {{ row.Enabled ? '禁用' : '启用' }}
            </el-button>
            <el-button
              type="danger"
              size="small"
              text
              @click="deleteModel(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑模型' : '添加模型'"
      width="600px"
      class="minimal-dialog"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item label="模型名称" prop="model_name">
          <el-input
            v-model="form.model_name"
            placeholder="请输入模型名称，如：gpt-3.5-turbo"
            :disabled="isEdit"
          />
        </el-form-item>
        <el-form-item label="输入配额(Token)" prop="tokens_input_per_quota">
          <el-input
            v-model.number="form.tokens_input_per_quota"
            type="number"
            placeholder="1个配额可使用的输入token数量，如：1000000"
          />
          <div style="font-size: 12px; color: #999; margin-top: 4px;">
            表示1个配额可以使用多少个输入token，如：1000000表示1配额=100万输入token
          </div>
        </el-form-item>
        <el-form-item label="输出配额(Token)" prop="tokens_output_per_quota">
          <el-input
            v-model.number="form.tokens_output_per_quota"
            type="number"
            placeholder="1个配额可使用的输出token数量，如：1000000"
          />
          <div style="font-size: 12px; color: #999; margin-top: 4px;">
            表示1个配额可以使用多少个输出token，如：1000000表示1配额=100万输出token
          </div>
        </el-form-item>
        <el-form-item label="最大Token" prop="max_tokens">
          <el-input
            v-model.number="form.max_tokens"
            type="number"
            placeholder="模型支持的最大token数，如：4096"
          />
        </el-form-item>
        <el-form-item label="温度" prop="temperature">
          <el-input-number
            v-model="form.temperature"
            :min="0"
            :max="2"
            :step="0.1"
            :precision="1"
            placeholder="控制输出随机性"
          />
        </el-form-item>
        <el-form-item label="Top P" prop="top_p">
          <el-input-number
            v-model="form.top_p"
            :min="0"
            :max="1"
            :step="0.1"
            :precision="1"
            placeholder="核采样参数"
          />
        </el-form-item>
        <el-form-item label="状态" prop="enabled">
          <el-radio-group v-model="form.enabled">
            <el-radio :label="true">启用</el-radio>
            <el-radio :label="false">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="submitting"
          @click="submitForm"
        >
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import api from '@/utils/api'

const route = useRoute()
const router = useRouter()

// 获取服务账号名称
const providerName = ref(route.params.provider || '')

// 响应式数据
const loading = ref(false)
const models = ref([])
const dialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)
const formRef = ref()

// 表单数据
const form = reactive({
  model_name: '',
  tokens_input_per_quota: 1000000,
  tokens_output_per_quota: 1000000,
  max_tokens: 4096,
  temperature: 0.7,
  top_p: 1.0,
  enabled: true
})

// 表单验证规则
const rules = {
  model_name: [
    { required: true, message: '请输入模型名称', trigger: 'blur' }
  ],
  tokens_input_per_quota: [
    { required: true, message: '请输入输入配额数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '配额数量必须大于0', trigger: 'blur' }
  ],
  tokens_output_per_quota: [
    { required: true, message: '请输入输出配额数量', trigger: 'blur' },
    { type: 'number', min: 1, message: '配额数量必须大于0', trigger: 'blur' }
  ]
}

// 返回服务账号列表
const goBack = () => {
  router.push('/providers')
}

// 加载模型列表
const loadModels = async () => {
  loading.value = true
  try {
    const response = await api.get(`/providers/models/${providerName.value}`)
    models.value = response.data.data || []
  } catch (error) {
    ElMessage.error('加载模型列表失败')
  } finally {
    loading.value = false
  }
}

// 显示添加对话框
const showAddDialog = () => {
  isEdit.value = false
  resetForm()
  dialogVisible.value = true
}

// 显示编辑对话框
const showEditDialog = (row) => {
  isEdit.value = true
  Object.assign(form, {
    model_name: row.ModelName,
    tokens_input_per_quota: row.TokensInputPerQuota,
    tokens_output_per_quota: row.TokensOutputPerQuota,
    max_tokens: row.MaxTokens,
    temperature: row.Temperature,
    top_p: row.TopP,
    enabled: row.Enabled
  })
  dialogVisible.value = true
}

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    model_name: '',
    tokens_input_per_quota: 1000000,
    tokens_output_per_quota: 1000000,
    max_tokens: 4096,
    temperature: 0.7,
    top_p: 1.0,
    enabled: true
  })
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    submitting.value = true
    
    const data = {
      ...form,
      provider: providerName.value
    }
    
    if (isEdit.value) {
      await api.put(`/providers/models/${providerName.value}/${form.model_name}`, data)
      ElMessage.success('更新成功')
    } else {
      await api.post(`/providers/models/${providerName.value}`, data)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    loadModels()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
  } finally {
    submitting.value = false
  }
}

// 切换状态
const toggleStatus = async (row) => {
  try {
    const newStatus = !row.Enabled
    await api.patch(`/providers/models/${providerName.value}/${row.ModelName}/status`, { enabled: newStatus })
    row.Enabled = newStatus
    ElMessage.success('状态更新成功')
  } catch (error) {
    ElMessage.error('状态更新失败')
  }
}

// 删除模型
const deleteModel = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模型 "${row.ModelName}" 吗？删除后可以通过数据库恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await api.delete(`/providers/models/${providerName.value}/${row.ModelName}`)
    ElMessage.success('删除成功（软删除，可通过数据库恢复）')

    // 重新加载模型列表以获取最新状态（软删除后的数据不会再显示）
    loadModels()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

// 组件挂载时加载数据
onMounted(() => {
  if (!providerName.value) {
    ElMessage.error('缺少服务账号参数')
    router.push('/providers')
    return
  }
  loadModels()
})
</script>

<style scoped>
.provider-models-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-actions {
  display: flex;
  gap: 12px;
}

:deep(.minimal-dialog .el-dialog__header) {
  padding: 24px 24px 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.minimal-dialog .el-dialog__body) {
  padding: 24px;
}

:deep(.minimal-dialog .el-dialog__footer) {
  padding: 0 24px 24px;
  border-top: 1px solid #f0f0f0;
}
</style>
