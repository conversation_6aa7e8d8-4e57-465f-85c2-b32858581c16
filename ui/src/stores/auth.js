import { defineStore } from 'pinia'
import { ref } from 'vue'
import api from '@/utils/api'

export const useAuthStore = defineStore('auth', () => {
  const token = ref(localStorage.getItem('token') || '')
  const isAuthenticated = ref(!!token.value)

  const login = async (loginKey) => {
    try {
      const response = await api.post('/login', { key: loginKey })
      
      if (response.data.success) {
        token.value = response.data.token
        isAuthenticated.value = true
        localStorage.setItem('token', token.value)
        return { success: true }
      } else {
        return { success: false, message: response.data.message || '登录失败' }
      }
    } catch (error) {
      console.error('Login error:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '网络错误，请稍后重试' 
      }
    }
  }

  const logout = () => {
    token.value = ''
    isAuthenticated.value = false
    localStorage.removeItem('token')
  }

  const checkAuth = () => {
    const savedToken = localStorage.getItem('token')
    if (savedToken) {
      token.value = savedToken
      isAuthenticated.value = true
    }
  }

  return {
    token,
    isAuthenticated,
    login,
    logout,
    checkAuth
  }
})
