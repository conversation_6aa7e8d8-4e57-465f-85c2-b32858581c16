<template>
  <div class="layout-container">
    <!-- 顶部导航 -->
    <el-header class="layout-header">
      <div class="header-left">
        <h1 style="font-size: 20px; font-weight: 600; color: #1f2937;">
          AI Gateway Admin
        </h1>
      </div>
      <div class="header-right">
        <el-dropdown @command="handleCommand">
          <span class="user-info">
            <el-icon><User /></el-icon>
            <span style="margin-left: 8px;">管理员</span>
            <el-icon style="margin-left: 4px;"><ArrowDown /></el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="logout">
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <el-container style="height: calc(100vh - 64px); overflow: hidden;">
      <!-- 侧边栏 -->
      <el-aside class="layout-sidebar">
        <el-menu
          :default-active="$route.path"
          router
          class="sidebar-menu"
          :collapse="false"
        >
          <el-menu-item index="/providers">
            <el-icon><Setting /></el-icon>
            <span>服务账号管理</span>
          </el-menu-item>
          <el-menu-item index="/model-groups">
            <el-icon><Grid /></el-icon>
            <span>模型组管理</span>
          </el-menu-item>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-main class="layout-content">
        <router-view />
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const authStore = useAuthStore()
const router = useRouter()

const handleCommand = (command) => {
  if (command === 'logout') {
    authStore.logout()
    ElMessage.success('已退出登录')
    router.push('/login')
  }
}
</script>

<style scoped>
.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s;
  color: #374151;
}

.user-info:hover {
  background-color: #f3f4f6;
}

.sidebar-menu {
  border-right: none;
  height: 100%;
}

.sidebar-menu .el-menu-item {
  height: 56px;
  line-height: 56px;
  margin: 4px 12px;
  border-radius: 6px;
  color: #6b7280;
}

.sidebar-menu .el-menu-item:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.sidebar-menu .el-menu-item.is-active {
  background-color: #eff6ff;
  color: #2563eb;
  font-weight: 500;
}

.sidebar-menu .el-menu-item.is-active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background-color: #2563eb;
  border-radius: 0 2px 2px 0;
}
</style>
