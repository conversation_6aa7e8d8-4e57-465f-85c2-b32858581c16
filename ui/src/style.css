/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f5f7fa;
  color: #333;
}

#app {
  height: 100vh;
  overflow: hidden;
}

/* 极简风格样式 */
.minimal-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: none;
}

.minimal-card .el-card__body {
  height: 100%;
  padding: 0;
}

.minimal-button {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.minimal-input {
  border-radius: 6px;
  border: 1px solid #e1e5e9;
  transition: border-color 0.2s ease;
}

.minimal-input:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

/* 布局样式 */
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.layout-header {
  background: white;
  border-bottom: 1px solid #e1e5e9;
  padding: 0 24px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.layout-sidebar {
  width: 240px;
  background: white;
  border-right: 1px solid #e1e5e9;
  height: calc(100vh - 64px);
  overflow-y: auto;
  flex-shrink: 0;
}

.layout-content {
  flex: 1;
  padding: 24px;
  background: #f5f7fa;
  overflow-y: auto;
  height: calc(100vh - 64px);
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.page-description {
  color: #6b7280;
  font-size: 14px;
}

/* 表格样式 */
.minimal-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.minimal-table .el-table__header {
  background: #f8fafc;
}

.minimal-table .el-table th {
  background: #f8fafc;
  color: #374151;
  font-weight: 600;
  border-bottom: 1px solid #e5e7eb;
}

.minimal-table .el-table td {
  border-bottom: 1px solid #f3f4f6;
}

/* 状态标签 */
.status-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-active {
  background: #dcfce7;
  color: #166534;
}

.status-inactive {
  background: #fee2e2;
  color: #991b1b;
}

/* 登录页面样式 */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
  width: 400px;
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.login-title {
  text-align: center;
  font-size: 28px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 32px;
}

.login-form .el-form-item {
  margin-bottom: 24px;
}

.login-button {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
}

/* 响应式 */
@media (max-width: 768px) {
  .layout-sidebar {
    width: 200px;
  }
  
  .layout-content {
    padding: 16px;
  }
  
  .login-card {
    width: 90%;
    margin: 0 20px;
    padding: 32px 24px;
  }
}
