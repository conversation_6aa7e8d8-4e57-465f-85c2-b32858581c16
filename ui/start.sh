#!/bin/bash

# AI Gateway Admin Panel 启动脚本

echo "🚀 启动 AI Gateway Admin Panel"
echo "================================"

# 检查 Node.js 是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js (版本 >= 16)"
    exit 1
fi

# 检查 npm 是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装 npm"
    exit 1
fi

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
fi

# 启动开发服务器
echo "🌟 启动开发服务器..."
echo "📍 访问地址: http://localhost:3000"
echo "🔑 请使用配置文件中的管理员密钥登录"
echo "================================"

npm run dev
