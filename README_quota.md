# 配额检查和请求限速模块

## 快速开始

### 1. 编译演示程序

```bash
# 编译配额演示程序
go build -o bin/quota_demo cmd/quota_demo/main.go
```

### 2. 启动演示服务

```bash
# 启动服务（确保数据库配置正确）
./bin/quota_demo --config=etc/config.yaml
```

服务将在 `http://localhost:8080` 启动。

### 3. 测试配额功能

```bash
# 运行测试脚本
./scripts/test_quota.sh
```

或者手动测试：

```bash
# 发送聊天请求
curl -X POST "http://localhost:8080/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "X-User-ID: test_user" \
  -H "X-Group: premium" \
  -d '{
    "model": "gpt-4",
    "messages": [{"role": "user", "content": "Hello!"}],
    "max_tokens": 100
  }'

# 查询配额状态
curl -X GET "http://localhost:8080/v1/quota/status" \
  -H "X-User-ID: test_user" \
  -H "X-Group: premium"

# 重置配额（管理员）
curl -X POST "http://localhost:8080/v1/admin/quota/reset/premium"
```

## 核心功能

### ✅ 配额管理
- 用户级别和组级别的配额控制
- 基于模型 token 消耗的精确计算
- 自动创建默认配额配置
- 支持配额重置和状态查询

### ✅ 请求限速
- 基于令牌桶算法的限速控制
- 支持总体速率限制和单次查询限制
- 用户级别和组级别的独立限速
- 动态限速配置更新

### ✅ 统一管理
- 配额和限速的统一检查接口
- Gin 中间件集成
- 异步配额消费，不影响响应时间
- 自动清理和内存管理

### ✅ HTTP 接口
- RESTful API 设计
- 标准 HTTP 状态码
- 详细的错误信息和重试建议
- 管理员接口支持

## 数据库表

系统使用以下数据库表存储配额和限速配置：

- `gw_group_quota_configs` - 组配额配置
- `gw_user_quota_configs` - 用户配额配置  
- `gw_group_limit_configs` - 组限速配置
- `gw_user_limit_configs` - 用户限速配置
- `gw_model_configs` - 模型配置（用于计算配额消耗）

## 配置示例

### 默认配额配置
- 用户默认配额：100,000 配额单位
- 组默认配额：500,000 配额单位

### 默认限速配置
- 用户限速：1,000 配额/秒，单次查询 100 配额
- 组限速：5,000 配额/秒，单次查询 500 配额

### 配额计算公式
```
配额消耗 = 输入token数 / 模型输入配额比率 + 输出token数 / 模型输出配额比率
```

## 集成到现有项目

### 1. 添加依赖
```go
import (
    "goalfy_aigateway/internal/quota"
    "goalfy_aigateway/internal/middleware"
    "goalfy_aigateway/internal/storage"
)
```

### 2. 初始化管理器
```go
// 创建配额限速管理器
db, _ := storage.Open()
quotaManager := quota.NewQuotaLimiterManager(db)
quotaManager.StartCleanupRoutine()
```

### 3. 添加中间件
```go
quotaMiddleware := middleware.NewQuotaMiddleware(quotaManager)

r.Use(quotaMiddleware.QuotaCheckMiddleware())
r.POST("/api/chat", 
    quotaMiddleware.PreQuotaCheckMiddleware(),
    yourHandler,
    quotaMiddleware.PostQuotaConsumeMiddleware(),
)
```

### 4. 在处理器中设置模型信息
```go
func yourHandler(c *gin.Context) {
    // 设置模型信息用于配额计算
    c.Set("provider", "openai")
    c.Set("model", "gpt-4")
    c.Set("input_tokens", 100)
    c.Set("output_tokens", 200)
    
    // 业务逻辑...
    
    // 设置实际使用的token数
    c.Set("actual_input_tokens", 95)
    c.Set("actual_output_tokens", 180)
}
```

## 监控和运维

### 配额使用监控
```go
status, err := quotaManager.GetStatus("user_id", "group")
// 检查配额使用率，发送告警等
```

### 定期配额重置
```go
// 每日重置配额
err := quotaManager.ResetQuota("group_name")
```

### 动态调整限速
```go
// 调整用户限速配置
err := quotaManager.UpdateLimitConfig("user_id", "group", 2000.0, 200.0)
```

## 错误处理

### 配额不足 (402 Payment Required)
```json
{
  "error": "配额不足: 用户配额不足",
  "code": "QUOTA_EXCEEDED",
  "quota_cost": 15.5,
  "retry_after": 3600
}
```

### 限速超限 (429 Too Many Requests)
```json
{
  "error": "限速超限: 用户限速超限", 
  "code": "RATE_LIMIT_EXCEEDED",
  "quota_cost": 10.0,
  "retry_after": 60
}
```

## 性能特性

- **高性能**: 基于内存的限速器，微秒级响应
- **可扩展**: 支持多实例部署
- **容错性**: 配额检查失败不影响核心业务
- **异步处理**: 配额消费异步执行，不影响响应时间

## 注意事项

1. 限速器状态存储在内存中，重启服务会重置
2. 配额更新使用数据库事务确保一致性
3. 建议定期监控配额使用情况
4. 多实例部署时限速器状态不共享

详细文档请参考 `docs/quota_limiter_usage.md`
